[project]
name = "raven"
authors = [
    { name = "The Commit Company", email = "<EMAIL>"}
]
description = "Simple, open source messaging tool"
requires-python = ">=3.10"
readme = "README.md"
dynamic = ["version"]
dependencies = [
 "linkpreview~=0.9.0",
 "openai",
 "blurhash-python",
 "openai-agents>=0.0.16",
 "markitdown",
 "pandas",
 "google-cloud-documentai"
]

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.black]
line-length = 99

[tool.isort]
line_length = 99
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
indent = "\t"