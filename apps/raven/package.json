{"private": true, "name": "raven", "version": "2.6.1", "description": "Messaging Application", "workspaces": ["frontend", "apps/*", "packages/*"], "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "cd frontend && yarn dev", "build": "cd frontend && yarn build", "postinstall": "yarn install-web && yarn install-desk", "install-desk": "cd raven && yarn install", "install-web": "cd frontend && yarn install"}, "keywords": [], "author": "", "license": "AGPL-3.0-only", "devDependencies": {"turbo": "^2.3.3"}, "dependencies": {}}