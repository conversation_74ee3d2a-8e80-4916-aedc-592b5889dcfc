{"name": "<PERSON><PERSON><PERSON>", "forwardPorts": [8000, 9000, 6787], "remoteUser": "frappe", "settings": {"terminal.integrated.defaultProfile.linux": "bash", "debug.node.autoAttach": "disabled"}, "dockerComposeFile": "./docker-compose.yml", "service": "frappe", "workspaceFolder": "/workspace/frappe-bench", "postCreateCommand": "bash /workspace/scripts/init.sh", "shutdownAction": "stopCompose", "extensions": ["ms-python.python", "ms-vscode.live-server", "grapecity.gc-excelviewer", "mtxr.sqltools", "visualstudioexptteam.vscodeintellicode"]}