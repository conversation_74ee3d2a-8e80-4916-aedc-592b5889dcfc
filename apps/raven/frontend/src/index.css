@tailwind base;
@tailwind components;
@tailwind utilities;

*,
::before,
::after {
    border-width: 0;
    border-style: solid;
    border-color: theme('borderColor.DEFAULT', currentColor);
}

.cal-sans {
    font-family: 'Cal Sans';
}

a {
    color: inherit;
    text-decoration: none;
}

ol,
ul {
    margin: 0;
    padding: 0;
}

.radix-themes {
    --heading-font-family: 'Cal Sans', var(--default-font-family);
    ;
}

.rt-Button {
    font-family: 'Cal Sans', var(--default-font-family);
}

.not-cal {
    font-family: var(--default-font-family);
}

:root {
    --sidebar-width: 20rem;
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 0rem;
    }
}

html {
    scrollbar-gutter: stable;
}

body {
    text-rendering: optimizeLegibility;
    margin: 0;
    -webkit-font-smoothing: antialiased;
}

/* make header draggable on desktop app */
html header {
    -webkit-app-region: drag;
}

html header button {
    -webkit-app-region: no-drag;
}

/*
  Adds Utility to hide scrollbar to tailwind
    https://github.com/tailwindlabs/tailwindcss/discussions/2394
    https://github.com/tailwindlabs/tailwindcss/pull/5732
*/
@layer utilities {

    /* Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    .no-scrollbar {
        -ms-overflow-style: none;
        /* IE and Edge */
        scrollbar-width: none;
        /* Firefox */
    }
}


/* animations */
.slideInBottom {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    animation-name: slideInBottom;
}

@keyframes slideInBottom {
    from {
        opacity: 0;
        transform: translateY(30%);
        pointer-events: none;
    }

    to {
        opacity: 1;
        pointer-events: auto;
    }
}

/* animations */
.slideInTop {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    animation-name: slideInTop;
}

@keyframes slideInTop {
    from {
        opacity: 0;
        transform: translateY(-20%);
        pointer-events: none;
    }

    to {
        opacity: 1;
        pointer-events: auto;
        transform: translateY(0%);
    }
}

.fadeIn {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    animation-name: fadeIn;
    animation-timing-function: ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}