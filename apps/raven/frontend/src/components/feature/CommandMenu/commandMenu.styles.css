.light .command-menu {
    --gray: var(--gray-2);
    --bg-gray: white;
    --box-shadow: var(--shadow-3);
    --border: 1px solid var(--gray-1);
    --item: var(--gray-2);
    --item-hover: var(--gray-a3);
    --text: var(--gray-12);
    --text-alt: var(--gray-9);
}

.dark .command-menu {
    --gray: var(--gray-2);
    --bg-gray: transparent;
    --box-shadow: var(--shadow-3);
    --border: 1px solid var(--gray-1);
    --item: var(--gray-2);
    --item-hover: var(--gray-a4);
    --text: var(--gray-12);
    --text-alt: var(--gray-11);
}

.command-menu [cmdk-root] {
    /* max-width: 640px; */
    width: 100%;
    /* padding: 8px; */
    background: var(--bg-gray);
    /* border-radius: 12px; */
    overflow: hidden;
    /* border: 1px solid var(--border); */
    /* box-shadow: 0 16px 70px var(--box-shadow); */
    transition: transform 100ms ease;
}

.command-menu [cmdk-item] {
    content-visibility: auto;
    cursor: pointer;
    height: 32px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 8px;
    color: var(--text);
    user-select: none;
    will-change: background, color;
    transition: all 150ms ease;
    transition-property: none;
}

.command-menu [cmdk-item][aria-selected='true'] {
    background: var(--item-hover);
    color: var(--text);
}

.command-menu [cmdk-item][aria-disabled='true'] {
    color: var(--item);
    cursor: not-allowed;
}

.command-menu [cmdk-item]:hover {
    transition-property: background;
    background: var(--item-hover);
    color: var(--text);
}

.command-menu [cmdk-item]+[cmdk-item] {
    margin-top: 4px;
}

.command-menu [cmdk-search] {
    border: none;
    width: 100%;
    font-size: 14px;
    padding: 0 0 0 8px;
    outline: none;
    border-bottom: 1px solid var(--border);
    margin-bottom: 8px;
    border-radius: 0;
}

.command-menu [cmdk-input] {
    border: none;
    width: 100%;
    font-size: 14px;
    padding: 0.5rem 0px 0.5rem 0px;
    background: var(--bg-gray);
    outline: none;
    font-weight: 500;
    color: var(--gray-12);
    border-radius: 0;
}

.command-menu [cmdk-input]::placeholder {
    color: var(--gray-10);
    font-size: 14px;
    font-weight: 500;
}

.command-menu [cmdk-list] {
    min-height: 300px;
    /* height: var(--cmdk-list-height); */
    max-height: 500px;
    transition: height 100ms ease;
    scroll-padding-block-start: 8px;
    scroll-padding-block-end: 8px;
    overflow: auto;
    overscroll-behavior: contain;
}

@media screen and (max-width: 716px) {
    .command-menu [cmdk-list] {
        max-height: 80vh;
    }

    .command-menu [cmdk-item]:hover {
        background: transparent;
    }

}

.command-menu [cmdk-list]::-webkit-scrollbar {
    width: 6px;
}

.command-menu [cmdk-list]::-webkit-scrollbar-track {
    background-color: transparent;
    width: 10px;
}

.command-menu [cmdk-list]::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 10px;
}

.command-menu [cmdk-list]:hover::-webkit-scrollbar {
    width: 6px;
}

.command-menu [cmdk-list]:hover::-webkit-scrollbar-track {
    background-color: 'transparent';
    width: 10px;
}

.command-menu [cmdk-list]:hover::-webkit-scrollbar-thumb {
    background-color: var(--thumb-color);
    border-radius: 3px;
}

.command-menu [cmdk-separator] {
    height: 1px;
    width: 100%;
    background: var(--item);
    margin: 4px 0;
}

.command-menu [cmdk-group-heading] {
    user-select: none;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-alt);
    display: flex;
    align-items: center;
    margin: 1rem 0px 0.4rem 0px;
}

.command-menu [cmdk-empty] {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    white-space: pre-wrap;
    color: var(--text);
}

.command-menu [cmdk-badge] {
    height: 32px;
    background: var(--gray);
    display: inline-flex;
    align-items: center;
    padding: 0px 2px 0px 8px;
    font-size: 12px;
    color: var(--text);
    border-radius: 4px;
    user-select: none;
    text-transform: capitalize;
    font-weight: 500;
}

.command-menu [cmdk-footer] {
    display: flex;
    justify-content: right;
    margin-top: 10px;
    padding: 8px 12px;
    font-size: 12px;
    color: var(--text);
    border-top: 1px solid var(--border);
    border-radius: 0 0 10px 10px;
}

.command-menu [cmdk-shortcuts] {
    display: flex;
    margin-left: auto;
    gap: 8px;
}

.command-menu [cmdk-shortcuts] kbd {
    font-size: 12px;
    min-width: 20px;
    padding: 4px;
    height: 20px;
    border-radius: 4px;
    color: var(--text);
    background: var(--bg-gray);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
}