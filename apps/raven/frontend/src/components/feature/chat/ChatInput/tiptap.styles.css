.tiptap-editor p.is-editor-empty:first-child::before {
    color: var(--gray-9);
    content: attr(data-placeholder);
    float: left;
    font-size: 14px;
    height: 0;
    pointer-events: none;
}

.tiptap-editor.ProseMirror {
    min-height: 60px;
    max-height: 150px;
    overflow: auto;
    padding: var(--space-3);
    border-top-left-radius: var(--radius-2);
    border-top-right-radius: var(--radius-2);
}


.tiptap-editor.ProseMirror img {
    width: 200px;
    height: auto;
    object-fit: content;

}

.tiptap-editor.replying.ProseMirror {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.tiptap-editor.ProseMirror:focus {
    outline: 1.5px solid var(--color-focus-root);
    outline-offset: -1.5px;
}


@media screen and (max-width: 768px) {
    .tiptap-editor.ProseMirror {
        min-height: 20px;
        padding: 8px;
        max-height: 300px;
    }
}

.tiptap-editor blockquote {
    border-left: 3px solid var(--gray-11);
    padding-left: 0.8rem;
    margin: 1rem;
}

.tiptap-editor ul,
.tiptap-editor ol {
    padding-left: var(--space-5);
}

.tiptap-editor a,
.tiptap-editor .mention {
    color: var(--accent-11);
}

.tiptap-editor a {
    text-decoration: underline;
    cursor: pointer;
}

.tiptap pre {
    background: #0d0d0d;
    border-radius: 0.5rem;
    color: #fff;
    font-family: "JetBrainsMono", monospace;
    padding: 0.75rem 1rem;
}

.tiptap pre code {
    background: none;
    color: inherit;
    font-size: 0.8rem;
    padding: 0;
}

.tiptap pre .hljs-comment,
.tiptap pre .hljs-quote {
    color: #616161;
}

.tiptap pre .hljs-variable,
.tiptap pre .hljs-template-variable,
.tiptap pre .hljs-attribute,
.tiptap pre .hljs-tag,
.tiptap pre .hljs-name,
.tiptap pre .hljs-regexp,
.tiptap pre .hljs-link,
.tiptap pre .hljs-name,
.tiptap pre .hljs-selector-id,
.tiptap pre .hljs-selector-class {
    color: #f98181;
}

.tiptap pre .hljs-number,
.tiptap pre .hljs-meta,
.tiptap pre .hljs-built_in,
.tiptap pre .hljs-builtin-name,
.tiptap pre .hljs-literal,
.tiptap pre .hljs-type,
.tiptap pre .hljs-params {
    color: #fbbc88;
}

.tiptap pre .hljs-string,
.tiptap pre .hljs-symbol,
.tiptap pre .hljs-bullet {
    color: #b9f18d;
}

.tiptap pre .hljs-title,
.tiptap pre .hljs-section {
    color: #faf594;
}

.tiptap pre .hljs-keyword,
.tiptap pre .hljs-selector-tag {
    color: #70cff8;
}

.tiptap pre .hljs-emphasis {
    font-style: italic;
}

.tiptap pre .hljs-strong {
    font-weight: 700;
}