{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-05-10 16:36:21.445325", "doctype": "DocType", "engine": "InnoDB", "field_order": ["file_name", "file", "column_break_duoy", "file_type", "openai_file_id"], "fields": [{"fieldname": "file_name", "fieldtype": "Data", "in_list_view": 1, "label": "File Name", "read_only": 1, "unique": 1}, {"fieldname": "file", "fieldtype": "Attach", "in_list_view": 1, "label": "File", "reqd": 1}, {"fieldname": "file_type", "fieldtype": "Data", "label": "File Type", "read_only": 1}, {"fieldname": "openai_file_id", "fieldtype": "Data", "label": "OpenAI File ID", "read_only": 1}, {"fieldname": "column_break_duoy", "fieldtype": "Column Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-12 08:45:43.188440", "modified_by": "Administrator", "module": "Raven AI", "name": "Raven AI File Source", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>", "share": 1, "write": 1}], "row_format": "Dynamic", "search_fields": "file_name", "show_title_field_in_link": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "file_name"}