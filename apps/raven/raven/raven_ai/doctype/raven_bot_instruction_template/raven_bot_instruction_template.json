{"actions": [], "allow_rename": 1, "autoname": "field:template_name", "beta": 1, "creation": "2024-09-12 18:21:02.235237", "doctype": "DocType", "engine": "InnoDB", "field_order": ["template_name", "dynamic_instructions", "instruction"], "fields": [{"fieldname": "template_name", "fieldtype": "Data", "in_list_view": 1, "label": "Template Name", "reqd": 1, "unique": 1}, {"default": "0", "description": "Dynamic Instructions allow you to embed <PERSON><PERSON> tags in your instruction to the bot. Hence the instruction would be different based on the user who is calling the bot or the data in your system. These instructions are computed every time the bot is called. Check this if you want to embed things like Employee ID, Company Name etc in your instructions dynamically", "fieldname": "dynamic_instructions", "fieldtype": "Check", "in_list_view": 1, "in_standard_filter": 1, "label": "Dynamic Instructions"}, {"description": "You can use Jinja variables here to customize the instruction to the bot at run time if dynamic instructions are enabled.", "fieldname": "instruction", "fieldtype": "Long Text", "in_list_view": 1, "label": "Instruction", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-12-07 23:27:51.805208", "modified_by": "Administrator", "module": "Raven AI", "name": "Raven Bot Instruction Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": []}