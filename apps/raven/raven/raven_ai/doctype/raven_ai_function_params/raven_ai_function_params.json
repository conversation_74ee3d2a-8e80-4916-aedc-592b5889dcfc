{"actions": [], "allow_rename": 1, "creation": "2024-09-27 19:33:46.149867", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["fieldname", "child_table_name", "required", "do_not_ask_ai", "default_value", "options", "column_break_zwir", "type", "description"], "fields": [{"fieldname": "fieldname", "fieldtype": "Data", "in_list_view": 1, "label": "Fieldname", "reqd": 1}, {"default": "0", "fieldname": "required", "fieldtype": "Check", "in_list_view": 1, "label": "Required"}, {"fieldname": "default_value", "fieldtype": "Data", "in_list_view": 1, "label": "Default Value", "mandatory_depends_on": "eval: doc.do_not_ask_ai;"}, {"fieldname": "options", "fieldtype": "Small Text", "in_list_view": 1, "label": "Options"}, {"fieldname": "column_break_zwir", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description", "reqd": 1}, {"fieldname": "type", "fieldtype": "Select", "label": "Type", "options": "string\ninteger\nnumber\nfloat\nboolean", "reqd": 1}, {"default": "0", "fieldname": "do_not_ask_ai", "fieldtype": "Check", "label": "Do not ask AI to fill this variable"}, {"fieldname": "child_table_name", "fieldtype": "Data", "label": "Child Table Name"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-10-01 02:08:27.767802", "modified_by": "Administrator", "module": "Raven AI", "name": "Raven AI Function Params", "owner": "Administrator", "permissions": [], "sort_field": "creation", "sort_order": "DESC", "states": []}