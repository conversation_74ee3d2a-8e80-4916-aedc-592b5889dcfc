{"allow_roles": [{"role": "Raven User"}], "creation": "2023-12-08 06:50:09.241858", "docstatus": 0, "doctype": "Module Onboarding", "documentation_url": "https://thecommit.company/raven", "idx": 0, "is_complete": 0, "modified": "2025-01-06 19:31:16.034565", "modified_by": "Administrator", "module": "Raven", "name": "Raven", "owner": "Administrator", "steps": [{"step": "Introduction to <PERSON>"}, {"step": "Adding Users to Raven"}, {"step": "Review Raven Settings"}, {"step": "Access the Web App"}], "subtitle": "Simple, work messaging tool", "success_message": "Raven is setup!", "title": "Let's setup Raven"}