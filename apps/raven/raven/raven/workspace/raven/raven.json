{"app": "raven", "charts": [], "content": "[{\"id\":\"MKitHAnT4f\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><a href=\\\"/raven\\\">Raven</a></span>\",\"col\":12}},{\"id\":\"gBy2HFBNf4\",\"type\":\"onboarding\",\"data\":{\"onboarding_name\":\"Raven\",\"col\":12}},{\"id\":\"ZcawYU5brv\",\"type\":\"card\",\"data\":{\"card_name\":\"DocTypes\",\"col\":4}},{\"id\":\"Uox8EFjnuM\",\"type\":\"card\",\"data\":{\"card_name\":\"AI\",\"col\":4}},{\"id\":\"F85-QVSjIb\",\"type\":\"card\",\"data\":{\"card_name\":\"Integrations\",\"col\":4}}]", "creation": "2023-06-22 20:12:29.687295", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "message-1", "idx": 0, "is_hidden": 0, "label": "Raven", "links": [{"hidden": 0, "is_query_report": 0, "label": "AI", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Functions", "link_count": 0, "link_to": "Raven AI Function", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Instruction Templates", "link_count": 0, "link_to": "Raven Bot Instruction Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Saved Prompts", "link_count": 0, "link_to": "Raven Bot AI Prompt", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "DocTypes", "link_count": 8, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Raven Users", "link_count": 0, "link_to": "Raven User", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Workspaces", "link_count": 0, "link_to": "Raven Workspace", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Workspace Members", "link_count": 0, "link_to": "Raven Workspace Member", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Channels", "link_count": 0, "link_to": "Raven Channel", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Members", "link_count": 0, "link_to": "Raven Channel Member", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Messages", "link_count": 0, "link_to": "Raven Message", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Message Reactions", "link_count": 0, "link_to": "Raven Message Reaction", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON>", "link_count": 0, "link_to": "<PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Integrations", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Message Actions", "link_count": 0, "link_to": "Raven Message Action", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Document Notifications", "link_count": 0, "link_to": "Raven Document Notification", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2024-12-15 19:43:38.989720", "modified_by": "Administrator", "module": "Raven", "name": "Raven", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "roles": [], "sequence_id": 30.0, "shortcuts": [], "title": "Raven", "type": "Workspace"}