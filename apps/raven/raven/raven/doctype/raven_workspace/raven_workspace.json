{"actions": [], "allow_rename": 1, "autoname": "field:workspace_name", "creation": "2024-10-22 19:44:29.264507", "doctype": "DocType", "engine": "InnoDB", "field_order": ["workspace_name", "type", "can_only_join_via_invite", "description", "column_break_svnf", "logo", "only_admins_can_create_channels"], "fields": [{"default": "Private", "fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "Public\nPrivate", "reqd": 1}, {"default": "0", "fieldname": "can_only_join_via_invite", "fieldtype": "Check", "label": "Can only join via invite?"}, {"fieldname": "column_break_svnf", "fieldtype": "Column Break"}, {"fieldname": "logo", "fieldtype": "Attach Image", "label": "Logo"}, {"fieldname": "workspace_name", "fieldtype": "Data", "in_list_view": 1, "label": "Workspace Name", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"default": "0", "description": "If unchecked, any workspace member can create a channel", "fieldname": "only_admins_can_create_channels", "fieldtype": "Check", "label": "Only allow admins to create channels in the workspace"}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Raven Workspace Member", "link_fieldname": "workspace"}], "make_attachments_public": 1, "modified": "2025-02-15 17:41:08.057640", "modified_by": "Administrator", "module": "Raven", "name": "Raven Workspace", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>", "share": 1, "write": 1}, {"delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Raven User", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": []}