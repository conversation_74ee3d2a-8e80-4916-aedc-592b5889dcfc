{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2025-02-07 14:16:36.666515", "doctype": "DocType", "engine": "InnoDB", "field_order": ["user", "environment", "device_information", "column_break_wmua", "fcm_token"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1, "search_index": 1}, {"default": "Web", "fieldname": "environment", "fieldtype": "Select", "in_list_view": 1, "label": "Environment", "options": "Web\nMobile", "reqd": 1}, {"fieldname": "device_information", "fieldtype": "Data", "label": "Device Information", "length": 300}, {"fieldname": "column_break_wmua", "fieldtype": "Column Break"}, {"fieldname": "fcm_token", "fieldtype": "Small Text", "in_list_view": 1, "label": "FCM Token", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-02-07 15:05:59.524997", "modified_by": "Administrator", "module": "Raven", "name": "<PERSON>", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "Raven User", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": []}