{"actions": [], "creation": "2025-04-09 16:52:43.811064", "doctype": "DocType", "engine": "InnoDB", "field_order": ["webhook_url", "section_break_ygew", "channel_id", "column_break_guju", "bot"], "fields": [{"fieldname": "webhook_url", "fieldtype": "Read Only", "label": "Webhook URL"}, {"fieldname": "section_break_ygew", "fieldtype": "Section Break"}, {"fieldname": "column_break_guju", "fieldtype": "Column Break"}, {"fieldname": "bot", "fieldtype": "Link", "in_list_view": 1, "label": "Bot", "options": "Raven User", "reqd": 1}, {"fieldname": "channel_id", "fieldtype": "Link", "in_list_view": 1, "label": "Channel ID", "options": "Raven Channel", "reqd": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-09 17:50:42.984484", "modified_by": "Administrator", "module": "Raven Integrations", "name": "Raven Incoming Webhook", "naming_rule": "By script", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "select": 1, "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>", "select": 1, "share": 1, "write": 1}], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "channel_id"}