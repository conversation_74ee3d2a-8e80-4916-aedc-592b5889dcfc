{"actions": [], "allow_rename": 1, "autoname": "field:event_name", "creation": "2024-02-23 13:05:16.692071", "doctype": "DocType", "engine": "InnoDB", "field_order": ["event_name", "disabled", "event_frequency", "cron_expression", "bot", "column_break_pjem", "send_to", "channel", "dm", "content", "scheduler_event_id"], "fields": [{"fieldname": "event_name", "fieldtype": "Data", "in_list_view": 1, "label": "Name", "reqd": 1, "unique": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "event_frequency", "fieldtype": "Select", "label": "Event Frequency", "options": "Every Day\nEvery Day of the week\nDate of the month\n<PERSON>ron"}, {"fieldname": "cron_expression", "fieldtype": "Data", "label": "CRON Expression"}, {"description": "This Bot will be used to send the message.", "fieldname": "bot", "fieldtype": "Link", "label": "Bot", "options": "<PERSON>", "reqd": 1}, {"fieldname": "send_to", "fieldtype": "Select", "label": "Send to", "options": "Channel\nDM"}, {"depends_on": "eval: doc.send_to === \"Channel\"", "fieldname": "channel", "fieldtype": "Link", "label": "Channel", "options": "Raven Channel", "reqd": 1}, {"depends_on": "eval: doc.send_to === \"DM\"", "fieldname": "dm", "fieldtype": "Link", "label": "DM", "mandatory_depends_on": "eval: doc.send_to === \"DM\"", "options": "Raven Channel"}, {"fieldname": "content", "fieldtype": "Small Text", "label": "Content", "reqd": 1}, {"fieldname": "scheduler_event_id", "fieldtype": "Link", "label": "Scheduler Event ID", "options": "<PERSON>"}, {"fieldname": "column_break_pjem", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-12-07 23:15:03.384652", "modified_by": "Administrator", "module": "Raven Integrations", "name": "Raven Scheduler Event", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}