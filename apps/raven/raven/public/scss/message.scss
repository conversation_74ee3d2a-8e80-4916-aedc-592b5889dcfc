.message-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;

    .raven-message-item-header {

        .raven-message-item-header-name {
            font-weight: 600;
            font-size: 0.8rem;
            color: var(--text-color);
        }

        .raven-message-item-header-time {
            font-size: 0.70rem;
            padding-left: 0.3rem;
            margin-left: 0.3rem;
            border-left: 1px solid var(--border-color);
            color: var(--text-light);
        }
    }

    .raven-message-image-link {
        text-decoration: none;
        font-weight: 500;
        font-size: 0.85rem;
        color: var(--text-color);
        border-bottom: 1px solid var(--gray-400);
        padding-bottom: 0.05rem;

        &:hover {
            border-bottom: 1px solid var(--gray-600);
        }
    }


}

.raven-tiptap-renderer {

    color: var(--text-color);

    a,
    .user-mention,
    .channel-mention {
        color: var(--blue-500);
    }

    ol,
    ul {
        padding-left: 1rem;
    }

    blockquote {
        border-left: 2px solid var(--gray-400);
        padding-left: 0.5rem;
        margin-left: 0.5rem;
    }
}