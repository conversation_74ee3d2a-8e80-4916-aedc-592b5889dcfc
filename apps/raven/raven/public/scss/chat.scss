.raven-chat-view {
    transition: all 0.3s ease-in-out;
    overflow-x: hidden;
    width: var(--raven-content-width);
    clip-path: inset(0 0 0 0);

    .raven-chat-view-container {
        position: relative;
        width: var(--raven-content-width);
        height: var(--raven-content-height);
        overflow-x: hidden;

        .raven-chat-input {
            position: fixed;
            bottom: 0;
            display: flex;
            gap: 0.5rem;
            min-height: 60px;
            padding: 6px;
            border-top: 1px solid var(--border-color);
            width: var(--raven-content-width);

            textarea {
                width: 100%;
                max-height: 60px;
                min-height: 60px;
                resize: none;
            }

            .send-button {

                svg {
                    fill: var(--gray-8);
                }

                // padding-right: 4px;

                &:hover {
                    background-color: var(--control-bg);
                }
            }
        }

        .raven-message-stream-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 0.5rem;
            height: calc(var(--raven-content-height) - 68px);
            width: 100%;
            overflow-y: scroll;
            overflow-x: hidden;
        }
    }

}