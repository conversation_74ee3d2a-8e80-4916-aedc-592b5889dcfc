@font-face {
    font-family: "Cal Sans";
    src: url("/assets/raven/fonts/CalSans.woff2") format("woff2");
}

:root {
    --raven-header-height: 42px; // Equal to the height of the email widget
    --raven-expanded-height: 84vh;
    --raven-content-height: calc(var(--raven-expanded-height) - var(--raven-header-height));
    --raven-content-width: 400px;
}

.cal-sans {
    font-family: "Cal Sans", "Inter", sans-serif;
}

.raven-chat {
    position: fixed;
    width: var(--raven-content-width);
    background-color: var(--bg-color);
    box-shadow: var(--shadow-md);
    border-top: 1px solid var(--border-color);
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    overflow-x: hidden;
    z-index: 1000;
    bottom: 0;
    border-top-right-radius: var(--border-radius-md);
    border-top-left-radius: var(--border-radius-md);
    left: 60px;
}

.skeleton {
    background-color: var(--skeleton-bg);
    animation: 2s breathe infinite;
}

@keyframes breathe {
    0% {
        opacity: 0.5;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.5;
    }
}

.break-all {
    word-break: break-all;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

@import './header.scss';
@import './channels.scss';
@import './avatar.scss';
@import './chat.scss';
@import './dateSeparator.scss';
@import './message.scss';