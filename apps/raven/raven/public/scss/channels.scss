.raven-channel-list {
    overflow-y: auto;
    height: var(--raven-content-height);
    transition: all 0.3s ease-in-out;

    h4 {
        font-size: 0.6rem;
        font-weight: 500;
        text-transform: uppercase;
        color: var(--text-muted);
        padding: 0.5rem 0.4rem;
        margin: 0;
    }
}

.raven-channel-list-item {
    padding: 0.3rem 0.5rem;
    display: flex;
    width: 100%;
    background-color: transparent;
    gap: 0.5rem;
    justify-content: space-between;
    align-items: center;
    border: none;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;

    .channel-label {
        gap: 0.5rem;
        display: flex;
        align-items: center;
        font-family: 'Cal Sans', 'Inter', sans-serif;
    }

    &:hover {
        background-color: var(--bg-light-gray);
    }

    .skeleton {
        height: 1.6rem;
        display: block;
        width: 100%;
        border-radius: var(--border-radius-md);
    }

    .raven-channel-icon {
        height: 1.8rem;
        display: flex;
        fill: var(--heading-color);
        align-items: center;
        justify-content: center;
        width: 1.8rem;
    }

    .channel-unread-count {
        background-color: var(--red-avatar-bg);
        color: var(--red-avatar-color);
        padding: 0.1rem 0.5rem;
        margin-bottom: -0.3rem;
        margin-right: 4px;
        border-radius: var(--border-radius-sm);
        font-size: 0.7rem;
        font-weight: 700;
    }


}