import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Editor<PERSON><PERSON><PERSON><PERSON>, <PERSON>, React<PERSON><PERSON>er, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import Highlight from '@tiptap/extension-highlight'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import Mention from '@tiptap/extension-mention'
import { Plugin } from 'prosemirror-state'