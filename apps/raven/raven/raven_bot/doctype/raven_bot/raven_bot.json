{"actions": [], "allow_rename": 1, "autoname": "field:bot_name", "beta": 1, "creation": "2024-03-29 20:25:13.529714", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["bot_name", "image", "raven_user", "column_break_lhoo", "description", "is_standard", "module", "ai_tab", "is_ai_bot", "model_provider", "model", "temperature", "column_break_ebil", "debug_mode", "reasoning_effort", "top_p", "ai_section", "openai_assistant_id", "enable_code_interpreter", "column_break_khmi", "allow_bot_to_write_documents", "enable_file_search", "section_break_lwkx", "instruction", "dynamic_instructions", "bot_functions", "openai_vector_store_id", "file_sources", "document_parsing_tab", "use_google_document_parser", "google_document_processor_id"], "fields": [{"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "image", "fieldtype": "Attach Image", "label": "Image"}, {"fieldname": "bot_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Bot Name", "reqd": 1, "unique": 1}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "hidden": 1, "label": "Is Standard"}, {"fieldname": "module", "fieldtype": "Link", "hidden": 1, "label": "<PERSON><PERSON><PERSON>", "mandatory_depends_on": "eval: doc.is_standard == 1", "options": "<PERSON><PERSON><PERSON>"}, {"fieldname": "raven_user", "fieldtype": "Link", "in_standard_filter": 1, "label": "Raven User", "options": "Raven User", "read_only": 1, "set_only_once": 1}, {"fieldname": "column_break_lhoo", "fieldtype": "Column Break"}, {"depends_on": "eval: doc.is_ai_bot", "fieldname": "ai_section", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "is_ai_bot", "fieldtype": "Check", "label": "Is AI Bot?"}, {"default": "OpenAI", "depends_on": "eval:doc.is_ai_bot", "fieldname": "model_provider", "fieldtype": "Select", "label": "Model Provider", "options": "OpenAI\nLocal LLM"}, {"depends_on": "eval: doc.is_ai_bot", "documentation_url": "https://platform.openai.com/docs/assistants/overview", "fieldname": "openai_assistant_id", "fieldtype": "Data", "label": "OpenAI Assistant ID"}, {"fieldname": "column_break_khmi", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "allow_bot_to_write_documents", "fieldtype": "Check", "label": "Allow <PERSON> to Write Documents"}, {"fieldname": "ai_tab", "fieldtype": "Tab Break", "label": "AI"}, {"fieldname": "section_break_lwkx", "fieldtype": "Section Break"}, {"depends_on": "eval: doc.is_ai_bot;", "description": "You can use Jinja variables here to customize the instruction to the bot at run time if dynamic instructions are enabled.", "fieldname": "instruction", "fieldtype": "Long Text", "label": "Instruction"}, {"default": "0", "description": "Enable this if you want the bot to be able to read PDF files and scan them.\n\nFile search enables the assistant with knowledge from files that you upload. Once a file is uploaded, the assistant automatically decides when to retrieve content based on user requests.", "documentation_url": "https://platform.openai.com/docs/assistants/tools/file-search", "fieldname": "enable_file_search", "fieldtype": "Check", "label": "Enable File Search"}, {"default": "0", "depends_on": "eval: doc.is_ai_bot;", "description": "Dynamic Instructions allow you to embed <PERSON><PERSON> tags in your instruction to the bot. Hence the instruction would be different based on the user who is calling the bot or the data in your system. These instructions are computed every time the bot is called. Check this if you want to embed things like Employee ID, Company Name etc in your instructions dynamically", "fieldname": "dynamic_instructions", "fieldtype": "Check", "label": "Dynamic Instructions"}, {"depends_on": "eval: doc.is_ai_bot;", "fieldname": "bot_functions", "fieldtype": "Table", "label": "Bot Functions", "options": "Raven Bot Functions"}, {"default": "0", "description": " Enable this if you want the bot to be able to process files like Excel sheets or data from Insights.\n                    <br>\n                    OpenAI Assistants run code in a sandboxed environment (on OpenAI servers) to do this.", "documentation_url": "https://platform.openai.com/docs/assistants/tools/code-interpreter", "fieldname": "enable_code_interpreter", "fieldtype": "Check", "label": "Enable Code Interpreter"}, {"default": "0", "description": "If enabled, stack traces of errors will be sent as messages by the bot ", "fieldname": "debug_mode", "fieldtype": "Check", "label": "Debug Mode"}, {"default": "gpt-4o", "depends_on": "eval:doc.is_ai_bot", "description": "For OpenAI: gpt-4o, gpt-4, etc. For Local LLM: use model name from /v1/models endpoint", "fieldname": "model", "fieldtype": "Data", "label": "Model"}, {"description": "Only applicable for OpenAI o-series models", "fieldname": "reasoning_effort", "fieldtype": "Select", "label": "Reasoning Effort", "options": "low\nmedium\nhigh"}, {"fieldname": "file_sources", "fieldtype": "Table", "label": "File Sources", "options": "Raven AI Bot Files"}, {"fieldname": "openai_vector_store_id", "fieldtype": "Data", "label": "OpenAI Vector Store ID", "read_only": 1}, {"default": "1", "description": "What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.", "fieldname": "temperature", "fieldtype": "Float", "label": "Temperature", "non_negative": 1}, {"fieldname": "column_break_ebil", "fieldtype": "Column Break"}, {"default": "1", "description": "An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.\n\nWe generally recommend altering this or temperature but not both.", "fieldname": "top_p", "fieldtype": "Float", "label": "Top P"}, {"fieldname": "document_parsing_tab", "fieldtype": "Tab Break", "label": "Document Parsing"}, {"default": "0", "description": "When images or PDFs are uploaded to the agent, <PERSON> will automatically call Google Cloud APIs to parse the document and send it's results to the agent.", "fieldname": "use_google_document_parser", "fieldtype": "Check", "label": "Use Google Document/Vision AI to parse documents"}, {"fieldname": "google_document_processor_id", "fieldtype": "Data", "label": "Google Document Processor ID", "length": 400}], "grid_page_length": 50, "image_field": "image", "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-17 00:04:29.764884", "modified_by": "Administrator", "module": "<PERSON>", "name": "<PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}