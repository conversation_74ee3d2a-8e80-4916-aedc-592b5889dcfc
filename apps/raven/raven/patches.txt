[pre_model_sync]

[post_model_sync]
raven.patches.v1_2.create_raven_users
raven.patches.v1_3.create_raven_message_indexes #23
raven.patches.v1_3.update_all_messages_to_include_message_content #2
raven.patches.v1_3.update_all_messages_to_include_replied_message_content #2
raven.patches.v1_6.create_raven_channel_member_index
raven.patches.v1_6.migrate_older_raven_users #2
raven.patches.v2_0.migrate_existing_dm_threads
raven.patches.v2_0.create_default_workspace
raven.patches.v2_0.create_default_company_workspace_mapping
raven.patches.v2_4.add_unique_constraint_on_reactions #2
raven.patches.v2_5.migrate_ai_bots_to_openai_provider