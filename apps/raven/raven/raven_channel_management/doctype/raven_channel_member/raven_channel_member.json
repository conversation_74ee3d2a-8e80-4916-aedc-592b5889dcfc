{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2023-02-12 17:31:23.807704", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["channel_id", "user_id", "is_admin", "last_visit", "column_break_adqk", "is_synced", "linked_doctype", "linked_document", "notification_settings_section", "allow_notifications"], "fields": [{"fieldname": "channel_id", "fieldtype": "Link", "in_list_view": 1, "label": "Channel", "options": "Raven Channel", "reqd": 1, "search_index": 1}, {"fieldname": "user_id", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "Raven User", "reqd": 1, "search_index": 1}, {"default": "0", "fieldname": "is_admin", "fieldtype": "Check", "label": "Is Admin"}, {"fetch_if_empty": 1, "fieldname": "last_visit", "fieldtype": "Datetime", "label": "Last Visit", "reqd": 1}, {"fieldname": "notification_settings_section", "fieldtype": "Section Break", "label": "Notification Settings"}, {"default": "0", "fieldname": "allow_notifications", "fieldtype": "Check", "label": "Allow notifications"}, {"fieldname": "column_break_adqk", "fieldtype": "Column Break"}, {"fieldname": "linked_doctype", "fieldtype": "Link", "label": "Linked DocType", "options": "DocType", "read_only": 1}, {"fieldname": "linked_document", "fieldtype": "Dynamic Link", "label": "Linked Document", "options": "linked_doctype", "read_only": 1}, {"default": "0", "fieldname": "is_synced", "fieldtype": "Check", "label": "Is Synced", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-11-29 17:31:35.446387", "modified_by": "Administrator", "module": "Raven Channel Management", "name": "Raven Channel Member", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Raven User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}