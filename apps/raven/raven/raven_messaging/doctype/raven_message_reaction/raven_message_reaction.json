{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2023-05-08 12:00:26.094736", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reaction", "reaction_escaped", "message", "channel_id", "is_custom"], "fields": [{"fieldname": "reaction", "fieldtype": "Data", "in_list_view": 1, "label": "Reaction", "reqd": 1}, {"fieldname": "message", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Message", "options": "Raven Message", "reqd": 1, "search_index": 1}, {"fieldname": "reaction_escaped", "fieldtype": "Data", "in_list_view": 1, "label": "Reaction Escaped", "search_index": 1}, {"fieldname": "channel_id", "fieldtype": "Link", "label": "Channel ID", "options": "Raven Channel"}, {"default": "0", "fieldname": "is_custom", "fieldtype": "Check", "label": "Is Custom"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-01-04 04:43:58.861388", "modified_by": "Administrator", "module": "Raven Messaging", "name": "Raven Message Reaction", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"delete": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "report": 1, "role": "Raven User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}