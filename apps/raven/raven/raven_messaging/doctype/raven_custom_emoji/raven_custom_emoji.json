{"actions": [], "allow_rename": 1, "autoname": "field:emoji_name", "creation": "2025-01-04 03:06:50.669037", "doctype": "DocType", "engine": "InnoDB", "field_order": ["image", "emoji_name", "keywords"], "fields": [{"fieldname": "image", "fieldtype": "Attach Image", "label": "Image", "reqd": 1}, {"fieldname": "emoji_name", "fieldtype": "Data", "in_list_view": 1, "label": "Emoji Name", "reqd": 1, "unique": 1}, {"fieldname": "keywords", "fieldtype": "Data", "label": "Keywords"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-01-04 03:09:49.286615", "modified_by": "Administrator", "module": "Raven Messaging", "name": "Raven Custom Emoji", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "Raven User", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Raven User", "share": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON>", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": []}