meta {
  name: React
  type: http
  seq: 1
}

post {
  url: {{url}}:{{port}}/api/method/raven.api.reactions.react?message_id=7a46635aff&reaction=🐶
  body: none
  auth: none
}

query {
  message_id: 7a46635aff
  reaction: 🐶
  ~message_id: 4ba126d7ba
}

headers {
  Authorization: token {{api_key}}:{{api_secret}}
}

docs {
  API to toggle a reaction to a Message
  
  The API first checks if the user has permission for the message. If yes, then it checks if the user has already reacted to the message. If not, it creates a new reaction, else it deletes the existing reaction.
}
