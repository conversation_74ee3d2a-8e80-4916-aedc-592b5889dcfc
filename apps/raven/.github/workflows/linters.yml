name: Linters

on:
  pull_request:
    paths:
      - raven/**
  workflow_dispatch:
  push:
    branches: [main]
    paths:
      - raven/**

jobs:
  linters:
    name: Semantic Commits
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"
          cache: "pip"

      - name: Install and Run Pre-commit
        uses: pre-commit/action@v3.0.1

      - name: Download Semgrep rules
        run: git clone --depth 1 https://github.com/frappe/semgrep-rules.git frappe-semgrep-rules

      - name: Download semgrep
        run: pip install semgrep

      - name: Run Semgrep rules
        run: semgrep ci --config ./frappe-semgrep-rules/rules
