/* Basic editor styles */
.tiptap {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding-inline: 8px;
  outline: none !important;
}

.tiptap:focus {
  outline: none !important;
}

.ProseMirror {
  outline: none !important;
}

.ProseMirror:focus {
  outline: none !important;
}

.tiptap :first-child {
  margin-top: 0;
}

/* List styles */
.tiptap ul,
.tiptap ol {
  padding: 0 1rem;
  margin: 1.25rem 1rem 1.25rem 0.4rem;
}

.tiptap ul li p,
.tiptap ol li p {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* Heading styles */
.tiptap h1,
.tiptap h2,
.tiptap h3,
.tiptap h4,
.tiptap h5,
.tiptap h6 {
  line-height: 1.1;
  margin-top: 2.5rem;
  text-wrap: pretty;
}

.tiptap h1,
.tiptap h2 {
  margin-top: 3.5rem;
  margin-bottom: 1.5rem;
}

.tiptap h1 {
  font-size: 1.4rem;
}

.tiptap h2 {
  font-size: 1.2rem;
}

.tiptap h3 {
  font-size: 1.1rem;
}

.tiptap h4,
.tiptap h5,
.tiptap h6 {
  font-size: 1rem;
}

/* Code and preformatted text styles */
.tiptap code {
  background-color: rgb(234, 234, 234);
  border-radius: 0.4rem;
  color: rgb(0, 0, 0);
  font-size: 0.85rem;
  padding: 0.25em 0.3em;
}

.tiptap pre {
  background: rgb(29, 29, 32);
  border-radius: 0.5rem;
  color: rgb(255, 255, 255);
  font-family: 'JetBrainsMono', monospace;
  margin: 1.5rem 0;
  padding: 0.75rem 1rem;
}

.tiptap pre code {
  background: none;
  color: inherit;
  font-size: 0.8rem;
  padding: 0;
}

.tiptap blockquote {
  border-left: 3px solid rgb(175, 176, 180);
  margin: 1.5rem 0;
  padding-left: 1rem;
}

.tiptap hr {
  border: none;
  border-top: 1px solid rgb(220, 220, 225);
  margin: 2rem 0;
}

.bubble-menu {
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(220, 220, 225);
  border-radius: 0.7rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  padding: 0.2rem;
  gap: 2px;
  align-items: center;
}

.bubble-menu button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
}

/* Active state for buttons */
.bubble-menu button.is-active {
  background-color: rgb(87, 83, 198);
  color: rgb(255, 255, 255);
}

/* Container styles */
.tiptap-editor-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.tiptap a {
  color: rgb(87, 83, 198);
}

.dark {
  color: rgb(255, 255, 255);
}

@media (prefers-color-scheme: dark) {
  .tiptap code {
    background-color: rgb(37, 37, 39);
    color: rgb(255, 255, 255);
  }

  .tiptap pre {
    background: rgb(29, 29, 32);
  }

  .bubble-menu {
    background-color: rgb(18, 18, 18);
    border-color: rgb(80, 80, 84);
  }

  .bubble-menu button.is-active {
    background-color: rgb(91, 91, 214);
  }
}