{"expo": {"name": "Raven", "slug": "Raven", "scheme": "raven.thecommit.company", "version": "1.1.2", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "ios": {"supportsTablet": false, "appleTeamId": "W6346TTZQX", "bundleIdentifier": "raven.thecommit.company", "config": {"usesNonExemptEncryption": false}, "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"UIBackgroundModes": ["remote-notification", "fetch"]}, "entitlements": {"aps-environment": "production"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "softwareKeyboardLayoutMode": "pan", "package": "raven.thecommit.company", "googleServicesFile": "./google-services.json"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-router"], ["expo-secure-store"], ["expo-font", {"fonts": ["./assets/fonts/CalSans-SemiBold.otf"]}], "@react-native-firebase/app", "@react-native-firebase/messaging", "@react-native-firebase/crashlytics", "@react-native-firebase/perf", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}], "expo-video"], "extra": {"router": {"origin": false}, "eas": {"projectId": "3a173ddd-f22a-4f81-8279-40dcee6c29d5"}}, "owner": "the-commit-company"}}