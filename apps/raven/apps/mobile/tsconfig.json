{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "jsx": "react-jsx",
    "strict": true,
    "composite": true,
    "moduleResolution": "bundler",
    "baseUrl": ".",
    "paths": {
      /** Add all packages to the paths */
      "@raven/types/*": [
        "../../packages/types/*"
      ],
      "@raven/lib/*": [
        "../../packages/lib/*"
      ],
      "@assets/*": [
        "./assets/*"
      ],
      "@components/*": [
        "./components/*"
      ],
      "@hooks/*": [
        "./hooks/*"
      ],
      "@lib/*": [
        "./lib/*"
      ],
      "@theme/*": [
        "./theme/*"
      ]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    /** Use all files in the packages folder */
    "../../packages/**/*.ts",
    "../../packages/**/*.tsx",
    "nativewind-env.d.ts",
    "declarations.d.ts"
  ]
}