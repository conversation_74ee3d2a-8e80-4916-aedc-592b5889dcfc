{"name": "@raven/mobile", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "nuke": "rm -rf ./node_modules && rm -rf ./ios && rm -rf ./android && rm -rf ./.expo", "nuke:ios": "rm -rf ./ios", "nuke:android": "rm -rf ./android"}, "dependencies": {"@expo/dom-webview": "^0.0.2", "@expo/react-native-action-sheet": "^4.1.0", "@gorhom/bottom-sheet": "5.0.6", "@legendapp/list": "1.0.15", "@raven/lib": "*", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/app": "^21.11.0", "@react-native-firebase/crashlytics": "^21.11.0", "@react-native-firebase/messaging": "^21.11.0", "@react-native-firebase/perf": "^21.11.0", "@react-native-menu/menu": "^1.2.2", "@react-native-segmented-control/segmented-control": "2.5.4", "@react-navigation/drawer": "^7.0.0", "@rn-primitives/alert-dialog": "^1.1.0", "@rn-primitives/avatar": "^1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/hooks": "^1.1.0", "@rn-primitives/portal": "^1.1.0", "@rn-primitives/slot": "^1.1.0", "@shopify/flash-list": "1.7.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "expo": "~52.0.42", "expo-auth-session": "~6.0.3", "expo-build-properties": "~0.13.2", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.5", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-image": "~2.0.4", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.5", "expo-intent-launcher": "~12.0.2", "expo-linking": "~7.0.5", "expo-navigation-bar": "~4.0.9", "expo-network": "~7.0.5", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-sharing": "~13.0.1", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-video": "~2.0.5", "expo-web-browser": "~14.0.2", "frappe-react-sdk": "^1.9.0", "htmlparser2": "^10.0.0", "jotai": "^2.12.1", "markdown-it": "^14.1.0", "nativewind": "^4.1.23", "prettier-plugin-tailwindcss": "^0.6.9", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-controlled-mentions": "^2.2.5", "react-native-gesture-handler": "~2.20.2", "react-native-ios-context-menu": "^3.1.0", "react-native-ios-utilities": "^5.1.2", "react-native-keyboard-controller": "^1.15.0", "react-native-reanimated": "~3.16.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.5.0", "react-native-uitextview": "^1.4.0", "react-native-user-inactivity": "^1.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-native-zoom-toolkit": "^4.0.0", "rn-emoji-picker": "^1.1.6", "sonner-native": "^0.17.0", "tailwind-merge": "^2.5.5", "zeego": "^3.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@raven/types": "*", "@types/react": "~18.3.12", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5.3.3"}, "private": true}