{"actions": [], "creation": "2013-06-25 10:25:16", "description": "Settings for <PERSON><PERSON>", "doctype": "DocType", "document_type": "Other", "engine": "InnoDB", "field_order": ["customer_defaults_section", "cust_master_name", "customer_group", "column_break_4", "territory", "item_price_settings_section", "selling_price_list", "maintain_same_rate_action", "role_to_override_stop_action", "column_break_15", "maintain_same_sales_rate", "editable_price_list_rate", "validate_selling_price", "editable_bundle_item_rates", "allow_negative_rates_for_items", "allow_zero_qty_in_sales_order", "allow_zero_qty_in_quotation", "sales_transactions_settings_section", "so_required", "dn_required", "sales_update_frequency", "blanket_order_allowance", "column_break_5", "allow_multiple_items", "allow_against_multiple_purchase_orders", "allow_sales_order_creation_for_expired_quotation", "dont_reserve_sales_order_qty_on_sales_return", "hide_tax_id", "enable_discount_accounting"], "fields": [{"default": "Customer Name", "fieldname": "cust_master_name", "fieldtype": "Select", "in_list_view": 1, "label": "Customer Naming By", "options": "Customer Name\nNaming Series\nAuto Name"}, {"fieldname": "customer_group", "fieldtype": "Link", "in_list_view": 1, "label": "Default Customer Group", "options": "Customer Group"}, {"fieldname": "territory", "fieldtype": "Link", "in_list_view": 1, "label": "Default Territory", "options": "Territory"}, {"fieldname": "selling_price_list", "fieldtype": "Link", "in_list_view": 1, "label": "Default Price List", "options": "Price List"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "so_required", "fieldtype": "Select", "label": "Is Sales Order Required for Sales Invoice & Delivery Note Creation?", "options": "No\nYes"}, {"fieldname": "dn_required", "fieldtype": "Select", "label": "Is Delivery Note Required for Sales Invoice Creation?", "options": "No\nYes"}, {"default": "Each Transaction", "description": "How often should Project and Company be updated based on Sales Transactions?", "fieldname": "sales_update_frequency", "fieldtype": "Select", "label": "Sales Update Frequency in Company and Project", "options": "Monthly\nEach Transaction\nDaily", "reqd": 1}, {"default": "0", "fieldname": "maintain_same_sales_rate", "fieldtype": "Check", "label": "Maintain Same Rate Throughout Sales Cycle"}, {"default": "0", "fieldname": "editable_price_list_rate", "fieldtype": "Check", "label": "Allow User to Edit Price List Rate in Transactions"}, {"default": "0", "fieldname": "allow_multiple_items", "fieldtype": "Check", "label": "Allow Item to be Added Multiple Times in a Transaction"}, {"default": "0", "fieldname": "allow_against_multiple_purchase_orders", "fieldtype": "Check", "label": "Allow Multiple Sales Orders Against a Customer's Purchase Order"}, {"default": "0", "fieldname": "validate_selling_price", "fieldtype": "Check", "label": "Validate Selling Price for Item Against Purchase Rate or Valuation Rate"}, {"default": "0", "fieldname": "hide_tax_id", "fieldtype": "Check", "label": "Hide Customer's Tax ID from Sales Transactions"}, {"default": "Stop", "depends_on": "maintain_same_sales_rate", "fieldname": "maintain_same_rate_action", "fieldtype": "Select", "label": "Action if Same Rate is Not Maintained Throughout Sales Cycle", "mandatory_depends_on": "maintain_same_sales_rate", "options": "Stop\nWarn"}, {"depends_on": "eval: doc.maintain_same_sales_rate && doc.maintain_same_rate_action == 'Stop'", "fieldname": "role_to_override_stop_action", "fieldtype": "Link", "label": "Role Allowed to Override Stop Action", "options": "Role"}, {"fieldname": "customer_defaults_section", "fieldtype": "Section Break", "label": "Customer De<PERSON><PERSON>s"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "item_price_settings_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON>tings"}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "sales_transactions_settings_section", "fieldtype": "Section Break", "label": "Transaction Settings"}, {"default": "0", "fieldname": "editable_bundle_item_rates", "fieldtype": "Check", "label": "Calculate Product Bundle Price based on Child Items' Rates"}, {"default": "0", "description": "If enabled, additional ledger entries will be made for discounts in a separate Discount Account", "fieldname": "enable_discount_accounting", "fieldtype": "Check", "label": "Enable Discount Accounting for Selling"}, {"default": "0", "fieldname": "allow_sales_order_creation_for_expired_quotation", "fieldtype": "Check", "label": "Allow Sales Order Creation For Expired Quotation"}, {"default": "0", "fieldname": "dont_reserve_sales_order_qty_on_sales_return", "fieldtype": "Check", "label": "Don't Reserve Sales Order Qty on Sales Return"}, {"default": "0", "fieldname": "allow_negative_rates_for_items", "fieldtype": "Check", "label": "Allow Negative rates for Items"}, {"description": "Percentage you are allowed to sell beyond the Blanket Order quantity.", "fieldname": "blanket_order_allowance", "fieldtype": "Float", "label": "Blanket Order Allowance (%)"}, {"default": "0", "fieldname": "allow_zero_qty_in_sales_order", "fieldtype": "Check", "label": "allow_zero_qty_in_sales_order"}, {"default": "0", "fieldname": "allow_zero_qty_in_quotation", "fieldtype": "Check", "label": "allow_zero_qty_in_quotation"}], "grid_page_length": 50, "icon": "fa fa-cog", "idx": 1, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-07-11 08:53:36.762129", "modified_by": "Administrator", "module": "Selling", "name": "Selling <PERSON>", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "role": "Sales Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}