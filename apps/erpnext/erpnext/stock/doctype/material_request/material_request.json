{"actions": [], "allow_import": 1, "autoname": "naming_series:", "creation": "2013-03-07 14:48:38", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["type_section", "naming_series", "title", "material_request_type", "customer", "company", "column_break_2", "transaction_date", "schedule_date", "amended_from", "warehouse_section", "scan_barcode", "column_break_13", "set_from_warehouse", "column_break5", "set_warehouse", "items_section", "items", "terms_tab", "terms_section_break", "tc_name", "terms", "more_info_tab", "status_section", "status", "per_ordered", "column_break2", "transfer_status", "per_received", "printing_details", "letter_head", "column_break_31", "select_print_heading", "reference", "job_card", "column_break_35", "work_order", "connections_tab"], "fields": [{"fieldname": "type_section", "fieldtype": "Section Break", "options": "fa fa-pushpin"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "MAT-MR-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"allow_on_submit": 1, "default": "{material_request_type}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "material_request_type", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Purpose", "options": "Purchase\nMaterial Transfer\nMaterial Issue\nManufacture\nCustomer Provided", "reqd": 1}, {"depends_on": "eval:doc.material_request_type==\"Customer Provided\"", "fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer", "print_hide": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "schedule_date", "fieldtype": "Date", "in_list_view": 1, "label": "Required By"}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "print_width": "150px", "remember_last_selected_value": 1, "reqd": 1, "search_index": 1, "width": "150px"}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Material Request", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "items_section", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Items", "oldfieldname": "indent_details", "oldfieldtype": "Table", "options": "Material Request Item", "reqd": 1}, {"default": "Today", "fieldname": "transaction_date", "fieldtype": "Date", "label": "Transaction Date", "no_copy": 1, "oldfieldname": "transaction_date", "oldfieldtype": "Date", "print_width": "100px", "reqd": 1, "search_index": 1, "width": "100px"}, {"fieldname": "column_break2", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nSubmitted\nStopped\nCancelled\nPending\nPartially Ordered\nPartially Received\nOrdered\nIssued\nTransferred\nReceived", "print_hide": 1, "print_width": "100px", "read_only": 1, "search_index": 1, "width": "100px"}, {"depends_on": "eval:doc.per_ordered > 0", "fieldname": "per_ordered", "fieldtype": "Percent", "label": "% Ordered", "no_copy": 1, "oldfieldname": "per_ordered", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.per_received > 0", "fieldname": "per_received", "fieldtype": "Percent", "label": "% Received", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "printing_details", "fieldtype": "Section Break", "label": "Printing Details"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Select", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "options": "Print Heading", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "terms", "fieldname": "terms_section_break", "fieldtype": "Section Break", "oldfieldtype": "Section Break", "options": "fa fa-legal"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1, "report_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions Content", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "fieldname": "reference", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "job_card", "fieldtype": "Link", "label": "Job Card", "options": "Job Card", "print_hide": 1, "read_only": 1}, {"fieldname": "warehouse_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Items"}, {"fieldname": "set_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "label": "Set Target Warehouse", "options": "Warehouse"}, {"fieldname": "column_break5", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"depends_on": "eval:doc.material_request_type == 'Material Transfer'", "fieldname": "set_from_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Set Source Warehouse", "options": "Warehouse"}, {"allow_on_submit": 1, "depends_on": "eval:doc.add_to_transit == 1", "fieldname": "transfer_status", "fieldtype": "Select", "label": "Transfer Status", "options": "\nNot Started\nIn Transit\nCompleted", "read_only": 1}, {"fieldname": "work_order", "fieldtype": "Link", "label": "Work Order", "options": "Work Order", "read_only": 1}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"collapsible": 1, "fieldname": "status_section", "fieldtype": "Section Break", "label": "Status", "oldfieldtype": "Section Break", "options": "fa fa-file-text"}, {"fieldname": "column_break_31", "fieldtype": "Column Break"}, {"fieldname": "column_break_35", "fieldtype": "Column Break"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}], "icon": "fa fa-ticket", "idx": 70, "is_submittable": 1, "links": [], "modified": "2025-04-21 18:36:04.827917", "modified_by": "Administrator", "module": "Stock", "name": "Material Request", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "set_user_permissions": 1, "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User", "share": 1, "submit": 1, "write": 1}], "quick_entry": 1, "search_fields": "status,transaction_date", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title"}