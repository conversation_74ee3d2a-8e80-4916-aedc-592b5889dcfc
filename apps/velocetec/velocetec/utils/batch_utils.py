#!/usr/bin/env python3
"""
Batch Utilities for Dimensioned Items
Handles automatic batch creation for dimensioned items across all creation scenarios
"""

import frappe
from frappe import _
from frappe.model.naming import make_autoname
import re


def create_batch_for_dimensioned_item(item_code, base_item_code=None, serial_no_series=None):
    """
    Create a batch record for a dimensioned item
    
    Args:
        item_code (str): The dimensioned item code (e.g., "Steel Bar - 25.0x100.0")
        base_item_code (str): The original item code (e.g., "Steel Bar")
        serial_no_series (str): Serial number series from the original item
    
    Returns:
        str: Name of the created batch record, or None if creation failed
    """
    try:
        # Check if item exists and has batch tracking enabled
        item_doc = frappe.get_doc("Item", item_code)
        if not item_doc.has_batch_no:
            frappe.log_error(
                message=f"Item {item_code} does not have batch tracking enabled",
                title="Batch Creation Skipped"
            )
            return None
        
        # Generate batch number using ERPNext's series generation
        batch_id = generate_batch_number_with_series(item_code, base_item_code, serial_no_series)

        # Check if batch already exists (shouldn't happen with proper series generation)
        if frappe.db.exists("Batch", batch_id):
            frappe.log_error(
                message=f"Batch {batch_id} already exists for item {item_code}",
                title="Batch Already Exists"
            )
            return batch_id
        
        # Create new batch record
        batch_doc = frappe.get_doc({
            "doctype": "Batch",
            "batch_id": batch_id,
            "item": item_code,
            "batch_qty": 0,  # Will be updated when stock entries are made
        })
        
        # Set expiry date if item has expiry tracking
        if item_doc.has_expiry_date and item_doc.shelf_life_in_days:
            from frappe.utils import add_days, nowdate
            batch_doc.expiry_date = add_days(nowdate(), item_doc.shelf_life_in_days)
        
        batch_doc.insert(ignore_permissions=True)
        
        frappe.logger().info(f"Created batch {batch_id} for dimensioned item {item_code}")
        return batch_id
        
    except Exception as e:
        frappe.log_error(
            message=f"Error creating batch for item {item_code}: {str(e)}",
            title="Batch Creation Error"
        )
        return None


def generate_batch_number_with_series(item_code, base_item_code=None, serial_no_series=None):
    """
    Generate a batch number using ERPNext's series generation like "201400000014"

    Args:
        item_code (str): The dimensioned item code
        base_item_code (str): The original item code
        serial_no_series (str): Serial number series from the original item

    Returns:
        str: Generated batch number using ERPNext series
    """
    try:
        # Create a series pattern based on the serial_no_series
        if serial_no_series:
            # Convert serial series to batch series
            # Example: "STEEL-BAR-.####" -> "STEEL-BAR-.########"
            batch_series = convert_serial_to_batch_series(serial_no_series)
        else:
            # Create a default series pattern
            batch_series = create_default_batch_series(item_code, base_item_code)

        # Use ERPNext's make_autoname to generate the batch number
        batch_id = make_autoname(batch_series)

        return batch_id

    except Exception as e:
        frappe.log_error(
            message=f"Error generating batch number with series for {item_code}: {str(e)}",
            title="Batch Series Generation Error"
        )
        # Fallback to simple sequential number
        return generate_fallback_batch_number(item_code)


def convert_serial_to_batch_series(serial_no_series):
    """
    Convert serial number series to batch series format

    Args:
        serial_no_series (str): Serial series like "STEEL-BAR-.####"

    Returns:
        str: Batch series like "2014.########"
    """
    try:
        # Use simple numeric prefix regardless of input
        # This ensures clean batch numbers like "201400000001"
        return "2014.########"

    except Exception as e:
        frappe.log_error(
            message=f"Error converting serial series {serial_no_series}: {str(e)}",
            title="Serial to Batch Conversion Error"
        )
        return "2014.########"


def create_numeric_prefix_from_text(text):
    """
    Create a simple numeric prefix from text for batch series

    Args:
        text (str): Text like "STEEL-BAR" or "ALU-PLATE"

    Returns:
        str: Simple numeric prefix like "2014"
    """
    try:
        # Just use a simple base prefix - let ERPNext handle the sequential numbering
        return "2014"

    except Exception as e:
        frappe.log_error(
            message=f"Error creating numeric prefix from {text}: {str(e)}",
            title="Numeric Prefix Creation Error"
        )
        return "2014"


def create_default_batch_series(item_code, base_item_code=None):
    """
    Create a default batch series when no serial series is available

    Args:
        item_code (str): The item code
        base_item_code (str): The base item code

    Returns:
        str: Default batch series
    """
    try:
        # Use simple numeric prefix
        numeric_prefix = create_numeric_prefix_from_text("")

        # Create series pattern
        return f"{numeric_prefix}.########"

    except Exception as e:
        frappe.log_error(
            message=f"Error creating default batch series: {str(e)}",
            title="Default Batch Series Error"
        )
        return "2014.########"


def generate_fallback_batch_number(item_code):
    """
    Generate a fallback batch number when series generation fails

    Args:
        item_code (str): The item code

    Returns:
        str: Fallback batch number
    """
    try:
        # Create a simple numeric batch ID
        import time
        timestamp = str(int(time.time()))[-8:]  # Last 8 digits of timestamp
        return f"201400{timestamp}"

    except Exception:
        # Ultimate fallback
        return f"201400000001"


def extract_dimensions_from_item_code(item_code):
    """
    Extract dimension suffix from item code
    
    Args:
        item_code (str): Item code with dimensions (e.g., "Steel Bar - 25.0x100.0")
    
    Returns:
        str: Dimension string (e.g., "25.0x100.0") or None if not found
    """
    # Pattern to match dimension suffixes like "25.0x100.0" or "30.0x20.0x10.0"
    dimension_pattern = r"-\s*([\d.]+x[\d.]+(?:x[\d.]+)?)\s*$"
    match = re.search(dimension_pattern, item_code)
    
    if match:
        return match.group(1).replace('.', '_')  # Replace dots with underscores for batch ID
    
    return None


def clean_serial_series_for_batch(serial_no_series):
    """
    Clean serial number series to create a batch prefix
    
    Args:
        serial_no_series (str): Serial number series (e.g., "STEEL-BAR-.####")
    
    Returns:
        str: Cleaned batch prefix (e.g., "STEEL-BAR-BATCH")
    """
    if not serial_no_series:
        return "BATCH"
    
    # Remove common serial patterns like .####, .###, etc.
    cleaned = re.sub(r'\.#+$', '', serial_no_series)
    
    # Remove trailing dots and dashes
    cleaned = cleaned.rstrip('.-')
    
    # Add BATCH suffix if not already present
    if 'BATCH' not in cleaned.upper():
        cleaned = f"{cleaned}-BATCH"
    
    # Ensure it's not too long (batch ID limit is 140 chars)
    return cleaned[:50]  # Leave room for dimensions and counter


def create_batch_prefix_from_item(item_code):
    """
    Create a batch prefix from item code
    
    Args:
        item_code (str): Item code
    
    Returns:
        str: Batch prefix
    """
    # Remove dimensions if present
    base_code = re.sub(r'\s*-\s*[\d.]+x[\d.]+(?:x[\d.]+)?\s*$', '', item_code)
    
    # Create abbreviation from item code
    words = base_code.split()
    if len(words) > 1:
        # Use first letter of each word
        prefix = ''.join([word[0].upper() for word in words if word])
    else:
        # Use first few characters
        prefix = base_code[:8].upper()
    
    # Remove special characters
    prefix = re.sub(r'[^A-Z0-9]', '', prefix)
    
    return f"{prefix}-BATCH" if prefix else "BATCH"


def ensure_unique_batch_id(batch_base):
    """
    Ensure batch ID is unique by adding counter if needed
    
    Args:
        batch_base (str): Base batch ID
    
    Returns:
        str: Unique batch ID
    """
    batch_id = batch_base
    counter = 1
    
    # Check if batch exists and increment counter until unique
    while frappe.db.exists("Batch", batch_id):
        batch_id = f"{batch_base}-{counter:03d}"
        counter += 1
        
        # Prevent infinite loop
        if counter > 999:
            batch_id = f"{batch_base}-{frappe.utils.random_string(3)}"
            break
    
    # Ensure batch ID doesn't exceed 140 character limit
    if len(batch_id) > 140:
        batch_id = batch_id[:137] + f"{counter:03d}"
    
    return batch_id


def create_batches_for_multiple_items(item_codes, base_item_mapping=None):
    """
    Create batches for multiple dimensioned items
    
    Args:
        item_codes (list): List of dimensioned item codes
        base_item_mapping (dict): Mapping of dimensioned item to base item info
                                 Format: {item_code: {"base_item": str, "serial_series": str}}
    
    Returns:
        dict: Mapping of item codes to created batch IDs
    """
    batch_mapping = {}
    
    for item_code in item_codes:
        base_info = base_item_mapping.get(item_code, {}) if base_item_mapping else {}
        base_item = base_info.get("base_item")
        serial_series = base_info.get("serial_series")
        
        batch_id = create_batch_for_dimensioned_item(item_code, base_item, serial_series)
        if batch_id:
            batch_mapping[item_code] = batch_id
    
    return batch_mapping


@frappe.whitelist()
def create_batch_for_item_api(item_code, base_item_code=None, serial_no_series=None):
    """
    API endpoint for creating batch for dimensioned item
    
    Args:
        item_code (str): The dimensioned item code
        base_item_code (str): The original item code
        serial_no_series (str): Serial number series from the original item
    
    Returns:
        dict: Result with batch ID or error message
    """
    try:
        batch_id = create_batch_for_dimensioned_item(item_code, base_item_code, serial_no_series)
        
        if batch_id:
            return {"success": True, "batch_id": batch_id}
        else:
            return {"success": False, "error": "Failed to create batch"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}
