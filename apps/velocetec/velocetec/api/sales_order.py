import copy
import frappe
from frappe import _
from frappe.utils.file_manager import get_file, save_file
from frappe.utils.response import build_response
import openpyxl
from io import BytesIO
import os
import json


@frappe.whitelist()
def update_delivery_dates_from_quotation_server(quotation_items):
    """
    Get delivery dates from quotation items for a new Sales Order.
    This function is called from the client-side when a new Sales Order with Velocetec Costing is loaded.

    Args:
        quotation_items (list): List of quotation item names

    Returns:
        dict: A dictionary with delivery dates for each quotation item
    """
    try:
        if not quotation_items:
            return {"status": "error", "message": "No quotation items provided"}

        if isinstance(quotation_items, str):
            import json

            try:
                quotation_items = json.loads(quotation_items)
            except Exception:
                quotation_items = [quotation_items]

        delivery_dates = {}
        for quotation_item in quotation_items:
            try:
                delivery_date = frappe.db.get_value(
                    "Quotation Item", quotation_item, "delivery_date"
                )
                if delivery_date:
                    delivery_dates[quotation_item] = delivery_date
            except Exception as e:
                frappe.log_error(
                    f"Error fetching delivery date for quotation item {quotation_item}: {str(e)}"
                )

        if delivery_dates:
            return {"status": "success", "delivery_dates": delivery_dates}
        else:
            return {
                "status": "info",
                "message": "No delivery dates found in the referenced quotation items.",
            }

    except Exception as e:
        frappe.log_error(f"Error getting delivery dates: {str(e)}")
        return {"status": "error", "message": f"Error getting delivery dates: {str(e)}"}


@frappe.whitelist()
def create_bom_from_so(sales_order_name):
    # 1. Fetch Sales Order and referenced Quotation
    so = frappe.get_doc("Sales Order", sales_order_name)
    if not so.items or not so.items[0].prevdoc_docname:
        frappe.throw("Sales Order does not reference a Quotation.")

    quotation_name = so.items[0].prevdoc_docname
    quotation = frappe.get_doc("Quotation", quotation_name)

    # 2. Retrieve linked Velocetec Costing (VC)
    if not so.velocetec_costing:
        frappe.throw(
            "The originating Quotation is not linked to any Velocetec Costing."
        )

    vc = frappe.get_doc("Velocetec Costing", so.velocetec_costing)

    # 3. Identify parent items from VC (top-level assemblies)
    vc_items = vc.get("items") or []
    parent_items = []
    for itm in vc_items:
        # Check if the item is a parent item (parent_part is None/empty)
        if itm.get("parent_part") in [None, "", "None"]:
            parent_val = itm.get("part_number")
            if parent_val and parent_val not in parent_items:
                parent_items.append(parent_val)

    # Filter out any None values
    parent_items = [p for p in parent_items if p is not None]

    if not parent_items:
        frappe.throw("No parent items found in the linked Velocetec Costing.")

    created_bom_ids = []

    # 4. Create a BOM Creator per parent
    for p_idx, parent_name in enumerate(parent_items, start=1):
        # Find the parent item record
        parent_item = None
        for x in vc_items:
            if x.get("part_number") == parent_name:
                parent_item = x
                break

        if not parent_item:
            frappe.msgprint(
                "Skipping " + parent_name + " as parent item record not found."
            )
            continue

        part_number = parent_item.get("part_number")
        bom_creator_name = "BOMC-" + parent_name

        # Check if this exact BOM Creator already exists
        existing_bom_creator = frappe.db.exists("BOM Creator", bom_creator_name)
        if existing_bom_creator:
            frappe.msgprint(f"BOM Creator {bom_creator_name} already exists. Skipping.")
            created_bom_ids.append(existing_bom_creator)
            continue

        # Create new BOM Creator
        bom_creator = frappe.new_doc("BOM Creator")
        bom_creator.name = bom_creator_name
        bom_creator.item_code = part_number
        bom_creator.item_name = part_number
        bom_creator.company = vc.company
        bom_creator.custom_sales_order = sales_order_name

        # Set VLC reference on BOM Creator
        if parent_item.get("velocetec_line_costing"):
            vlc_data = parent_item.get("velocetec_line_costing")
            if isinstance(vlc_data, str):
                try:
                    vlc_dict = json.loads(vlc_data)
                    bom_creator.custom_velocetec_line_costing = vlc_dict.get("name")
                except (json.JSONDecodeError, AttributeError):
                    bom_creator.custom_velocetec_line_costing = vlc_data
            elif isinstance(vlc_data, dict):
                bom_creator.custom_velocetec_line_costing = vlc_data.get("name")
            else:
                bom_creator.custom_velocetec_line_costing = vlc_data

        # Set quantity from Sales Order or fallback
        so_match = None
        for s in so.items:
            if s.item_code == part_number:
                so_match = s
                break
        if so_match:
            # Set qty to 1
            bom_creator.qty = 1
        else:
            # Set qty to 1
            bom_creator.qty = 1

        bom_creator.insert(ignore_permissions=True)
        frappe.db.commit()

        header_fg = parent_item.get("part_number")

        # 5. Raw materials at header level
        # Get VLC directly from the velocetec_line_costing field
        parent_vlc = None
        if parent_item.get("velocetec_line_costing"):
            vlc_data = parent_item.get("velocetec_line_costing")
            if isinstance(vlc_data, str):
                try:
                    vlc_dict = json.loads(vlc_data)
                    parent_vlc_name = vlc_dict.get("name")
                except (json.JSONDecodeError, AttributeError):
                    parent_vlc_name = vlc_data
            elif isinstance(vlc_data, dict):
                parent_vlc_name = vlc_data.get("name")
            else:
                parent_vlc_name = vlc_data

            if parent_vlc_name:
                parent_vlc = frappe.get_doc("Velocetec Line Costing", parent_vlc_name)

        if parent_vlc:
            raw_materials = parent_vlc.get("line_material_details") or []
            bar_materials = parent_vlc.get("bar_material_details") or []
            for rm in raw_materials + bar_materials:
                # Create dimensioned item with suffix if dimensions exist
                base_material_type = rm.get("material_type") or "UNKNOWN"
                # Determine material form from VLC table context
                material_form = "Block" if rm in raw_materials else "Bar"
                dimensioned_item_code = _create_dimensioned_item_if_needed(
                    rm, base_material_type, material_form
                )

                rm_row = bom_creator.append("items", {})
                rm_row.fg_item = header_fg
                rm_row.item_code = dimensioned_item_code
                rm_row.item_name = dimensioned_item_code
                rm_row.qty = rm.get("qty") or 1
                rm_row.rate = rm.get("amount") or 0
                rm_row.custom_cost = rm.get("amount") or 0
                rm_row.uom = "Nos"
                rm_row.parent_row_no = None
                rm_row.is_expandable = 0
                rm_row.do_not_explode = 1
                rm_row.sourced_by_supplier = 0
                rm_row.bom_created = 0
                if rm_row.meta and rm_row.meta.get_field("raw_material"):
                    rm_row.raw_material = 1

                # --- New Block: Set custom_material_size for raw materials ---
                size_record = None
                # If block material (x, y, z provided)
                if (
                    rm.get("x") is not None
                    and rm.get("y") is not None
                    and rm.get("z") is not None
                ):
                    # Construct size string exactly as "23x23x23"
                    size_str = (
                        str(rm.get("x"))
                        + "x"
                        + str(rm.get("y"))
                        + "x"
                        + str(rm.get("z"))
                    )
                    size_record = frappe.db.get_value(
                        "Velocetec Material Size",
                        {
                            "material_form": "Block",
                            "x_dim": rm.get("x"),
                            "y_dim": rm.get("y"),
                            "z_dim": rm.get("z"),
                        },
                        "name",
                    )
                    if not size_record:
                        new_size = frappe.get_doc(
                            {
                                "doctype": "Velocetec Material Size",
                                "material_form": "Block",
                                "x_dim": rm.get("x"),
                                "y_dim": rm.get("y"),
                                "z_dim": rm.get("z"),
                                "name": size_str,
                            }
                        )
                        new_size.insert(ignore_permissions=True)
                        size_record = new_size.name
                # Else, if bar material (d and l provided)
                elif rm.get("d") is not None and rm.get("l") is not None:
                    size_str = str(rm.get("d")) + "x" + str(rm.get("l"))
                    size_record = frappe.db.get_value(
                        "Velocetec Material Size",
                        {
                            "material_form": "Bar",
                            "d_dim": rm.get("d"),
                            "l_dim": rm.get("l"),
                        },
                        "name",
                    )
                    if not size_record:
                        new_size = frappe.get_doc(
                            {
                                "doctype": "Velocetec Material Size",
                                "material_form": "Bar",
                                "d_dim": rm.get("d"),
                                "l_dim": rm.get("l"),
                                "name": size_str,
                            }
                        )
                        new_size.insert(ignore_permissions=True)
                        size_record = new_size.name
                if size_record:
                    rm_row.custom_material_size = size_record
            # --- End New Block ---

            # --- New Block: Process line_fixings as raw materials ---
            fixings = parent_vlc.get("line_fixings") or []
            for fixing in fixings:
                fixing_row = bom_creator.append("items", {})
                fixing_row.fg_item = header_fg
                fixing_row.item_code = fixing.get("item") or "UNKNOWN"
                fixing_row.item_name = fixing.get("item") or "UNKNOWN"
                fixing_row.qty = fixing.get("qty") or 1
                fixing_row.rate = fixing.get("costunit") or 0
                fixing_row.custom_cost = fixing.get("costunit") or 0
                fixing_row.uom = fixing.get("uom") or "Nos"
                fixing_row.parent_row_no = None
                fixing_row.is_expandable = 0
                fixing_row.do_not_explode = 1
                fixing_row.sourced_by_supplier = 0
                fixing_row.bom_created = 0
                if fixing_row.meta and fixing_row.meta.get_field("raw_material"):
                    fixing_row.raw_material = 1
            # --- End New Block for fixings ---

        # 6. Process immediate children using a stack
        stack = []
        children = []
        for itm in vc_items:
            if itm.get("parent_part") == parent_item.get("part_number") and itm.get(
                "part_number"
            ) != parent_item.get("part_number"):
                children.append(itm)

        for child in children:
            # child is a sub-assembly under the header
            child_row = bom_creator.append("items", {})
            child_row_idx = child_row.idx

            child_row.item_code = child.get("part_number")
            child_row.item_name = child.get("part_number")
            # This ensures BOM represents per-unit requirements for manufacturing 1 parent item
            raw_child_qty = child.get("quantity") or 1
            parent_qty = parent_item.get("quantity") or 1
            normalized_child_qty = raw_child_qty / parent_qty
            child_row.qty = normalized_child_qty

            child_row.custom_cost = child.get("sell_price_each") or 0
            child_row.custom_cost = (
                child.get("sell_cost_each") or child.get("sell_price_each") or 0
            )
            child_row.uom = "Nos"

            # Mark immediate children as raw materials from the perspective of BOM Creator
            child_row.fg_item = header_fg  # so the BOM tree sees it under the parent
            child_row.is_expandable = 0  # treat as raw material
            child_row.do_not_explode = 1
            child_row.sourced_by_supplier = 0
            child_row.bom_created = 0

            # Set VLC reference on child
            child_vlc_name = None
            if child.get("velocetec_line_costing"):
                vlc_data = child.get("velocetec_line_costing")
                if isinstance(vlc_data, str):
                    try:
                        vlc_dict = json.loads(vlc_data)
                        child_vlc_name = vlc_dict.get("name")
                    except (json.JSONDecodeError, AttributeError):
                        child_vlc_name = vlc_data
                elif isinstance(vlc_data, dict):
                    child_vlc_name = vlc_data.get("name")
                else:
                    child_vlc_name = vlc_data
                child_row.custom_velocetec_line_costing = child_vlc_name

            # If the child has its own raw materials
            if child_vlc_name:
                child_vlc = frappe.get_doc("Velocetec Line Costing", child_vlc_name)
                raw_materials = child_vlc.get("line_material_details") or []
                bar_materials = child_vlc.get("bar_material_details") or []
                for rm in raw_materials + bar_materials:
                    # Create dimensioned item with suffix if dimensions exist
                    base_material_type = rm.get("material_type") or "UNKNOWN"
                    # Determine material form from VLC table context
                    material_form = "Block" if rm in raw_materials else "Bar"
                    dimensioned_item_code = _create_dimensioned_item_if_needed(
                        rm, base_material_type, material_form
                    )

                    rm_row = bom_creator.append("items", {})
                    rm_row.fg_item = child.get(
                        "part_number"
                    )  # nested under the child's code
                    rm_row.item_code = dimensioned_item_code
                    rm_row.item_name = dimensioned_item_code
                    rm_row.qty = rm.get("qty") or 1
                    rm_row.rate = rm.get("amount") or 0
                    rm_row.custom_cost = rm.get("amount") or 0
                    rm_row.uom = "Nos"
                    rm_row.parent_row_no = str(child_row_idx)
                    rm_row.is_expandable = 0
                    rm_row.do_not_explode = 1
                    rm_row.sourced_by_supplier = 0
                    rm_row.bom_created = 0
                    if rm_row.meta and rm_row.meta.get_field("raw_material"):
                        rm_row.raw_material = 1

                    # --- New Block: Set custom_material_size for child raw materials ---
                    size_record = None
                    if (
                        rm.get("x") is not None
                        and rm.get("y") is not None
                        and rm.get("z") is not None
                    ):
                        size_record = frappe.db.get_value(
                            "Velocetec Material Size",
                            {
                                "material_form": "Block",
                                "x_dim": rm.get("x"),
                                "y_dim": rm.get("y"),
                                "z_dim": rm.get("z"),
                            },
                            "name",
                        )
                        if not size_record:
                            new_size = frappe.get_doc(
                                {
                                    "doctype": "Velocetec Material Size",
                                    "material_form": "Block",
                                    "x_dim": rm.get("x"),
                                    "y_dim": rm.get("y"),
                                    "z_dim": rm.get("z"),
                                    "name": str(rm.get("x"))
                                    + "x"
                                    + str(rm.get("y"))
                                    + "x"
                                    + str(rm.get("z")),
                                }
                            )
                            new_size.insert(ignore_permissions=True)
                            size_record = new_size.name
                    elif rm.get("d") is not None and rm.get("l") is not None:
                        size_record = frappe.db.get_value(
                            "Velocetec Material Size",
                            {
                                "material_form": "Bar",
                                "d_dim": rm.get("d"),
                                "l_dim": rm.get("l"),
                            },
                            "name",
                        )
                        if not size_record:
                            new_size = frappe.get_doc(
                                {
                                    "doctype": "Velocetec Material Size",
                                    "material_form": "Bar",
                                    "d_dim": rm.get("d"),
                                    "l_dim": rm.get("l"),
                                    "name": str(rm.get("d")) + "x" + str(rm.get("l")),
                                }
                            )
                            new_size.insert(ignore_permissions=True)
                            size_record = new_size.name
                    if size_record:
                        rm_row.custom_material_size = size_record
                    # --- End New Block for child raw materials ---

                # --- New Block: Process line_fixings for child items ---
                fixings = child_vlc.get("line_fixings") or []
                for fixing in fixings:
                    fixing_row = bom_creator.append("items", {})
                    fixing_row.fg_item = child.get(
                        "part_number"
                    )  # nested under the child's code
                    fixing_row.item_code = fixing.get("item") or "UNKNOWN"
                    fixing_row.item_name = fixing.get("item") or "UNKNOWN"
                    fixing_row.qty = fixing.get("qty") or 1
                    fixing_row.rate = fixing.get("costunit") or 0
                    fixing_row.custom_cost = fixing.get("costunit") or 0
                    fixing_row.uom = fixing.get("uom") or "Nos"
                    fixing_row.parent_row_no = str(child_row_idx)
                    fixing_row.is_expandable = 0
                    fixing_row.do_not_explode = 1
                    fixing_row.sourced_by_supplier = 0
                    fixing_row.bom_created = 0
                    if fixing_row.meta and fixing_row.meta.get_field("raw_material"):
                        fixing_row.raw_material = 1
                # --- End New Block for child line_fixings ---

            # Check if this child has deeper sub-children
            sub_has_sub = False
            for x in vc_items:
                if x.get("parent_part") == child.get("part_number") and x.get(
                    "part_number"
                ) != child.get("part_number"):
                    sub_has_sub = True
                    break
            if sub_has_sub:
                stack.append({"child": child, "child_row_idx": child_row_idx})

        # 7. Process nested children
        while len(stack) > 0:
            popped = stack.pop()
            current_child = popped["child"]
            current_parent_idx = popped["child_row_idx"]

            sub_children = []
            for itm in vc_items:
                if itm.get("parent_part") == current_child.get(
                    "part_number"
                ) and itm.get("part_number") != current_child.get("part_number"):
                    sub_children.append(itm)

            for sub_child in sub_children:
                # sub_child is nested
                sub_child_row = bom_creator.append("items", {})
                sub_child_row_idx = sub_child_row.idx
                sub_child_row.item_code = sub_child.get("part_number")
                sub_child_row.item_name = sub_child.get("part_number")
                # For sub-children, divide by root parent quantity to get per-unit requirements
                raw_sub_child_qty = sub_child.get("quantity") or 1
                parent_qty = parent_item.get("quantity") or 1
                normalized_sub_child_qty = raw_sub_child_qty / parent_qty
                sub_child_row.qty = normalized_sub_child_qty

                # Use sell_cost_each instead of sell_price_each to get raw costs without markup
                sub_child_row.custom_cost = (
                    sub_child.get("sell_cost_each")
                    or sub_child.get("sell_price_each")
                    or 0
                )
                sub_child_row.uom = "Nos"

                # Mark as raw material so we can set parent_row_no
                sub_child_row.fg_item = current_child.get("part_number")
                sub_child_row.parent_row_no = str(current_parent_idx)
                sub_child_row.is_expandable = 0
                sub_child_row.do_not_explode = 1
                sub_child_row.sourced_by_supplier = 0
                sub_child_row.bom_created = 0

                # Set VLC reference on sub-child
                sub_child_vlc_name = None
                if sub_child.get("velocetec_line_costing"):
                    vlc_data = sub_child.get("velocetec_line_costing")
                    if isinstance(vlc_data, str):
                        try:
                            vlc_dict = json.loads(vlc_data)
                            sub_child_vlc_name = vlc_dict.get("name")
                        except (json.JSONDecodeError, AttributeError):
                            sub_child_vlc_name = vlc_data
                    elif isinstance(vlc_data, dict):
                        sub_child_vlc_name = vlc_data.get("name")
                    else:
                        sub_child_vlc_name = vlc_data
                    sub_child_row.custom_velocetec_line_costing = sub_child_vlc_name

                # If the sub_child has raw materials
                if sub_child_vlc_name:
                    sub_child_vlc = frappe.get_doc(
                        "Velocetec Line Costing", sub_child_vlc_name
                    )
                    raw_materials = sub_child_vlc.get("line_material_details") or []
                    bar_materials = sub_child_vlc.get("bar_material_details") or []
                    for rm in raw_materials + bar_materials:
                        # Create dimensioned item with suffix if dimensions exist
                        base_material_type = rm.get("material_type") or "UNKNOWN"
                        # Determine material form from VLC table context
                        material_form = "Block" if rm in raw_materials else "Bar"
                        dimensioned_item_code = _create_dimensioned_item_if_needed(
                            rm, base_material_type, material_form
                        )

                        rm_row = bom_creator.append("items", {})
                        rm_row.fg_item = sub_child.get("part_number")
                        rm_row.item_code = dimensioned_item_code
                        rm_row.item_name = dimensioned_item_code
                        rm_row.qty = rm.get("qty") or 1
                        rm_row.rate = rm.get("amount") or 0
                        rm_row.custom_cost = rm.get("amount") or 0
                        rm_row.uom = "Nos"
                        rm_row.parent_row_no = str(sub_child_row_idx)
                        rm_row.is_expandable = 0
                        rm_row.do_not_explode = 1
                        rm_row.sourced_by_supplier = 0
                        rm_row.bom_created = 0
                        if rm_row.meta and rm_row.meta.get_field("raw_material"):
                            rm_row.raw_material = 1

                        # --- New Block: Set custom_material_size for sub-child raw materials ---
                        size_record = None
                        if (
                            rm.get("x") is not None
                            and rm.get("y") is not None
                            and rm.get("z") is not None
                        ):
                            size_record = frappe.db.get_value(
                                "Velocetec Material Size",
                                {
                                    "material_form": "Block",
                                    "x_dim": rm.get("x"),
                                    "y_dim": rm.get("y"),
                                    "z_dim": rm.get("z"),
                                },
                                "name",
                            )
                            if not size_record:
                                new_size = frappe.get_doc(
                                    {
                                        "doctype": "Velocetec Material Size",
                                        "material_form": "Block",
                                        "x_dim": rm.get("x"),
                                        "y_dim": rm.get("y"),
                                        "z_dim": rm.get("z"),
                                        "name": str(rm.get("x"))
                                        + "x"
                                        + str(rm.get("y"))
                                        + "x"
                                        + str(rm.get("z")),
                                    }
                                )
                                new_size.insert(ignore_permissions=True)
                                size_record = new_size.name
                        elif rm.get("d") is not None and rm.get("l") is not None:
                            size_record = frappe.db.get_value(
                                "Velocetec Material Size",
                                {
                                    "material_form": "Bar",
                                    "d_dim": rm.get("d"),
                                    "l_dim": rm.get("l"),
                                },
                                "name",
                            )
                            if not size_record:
                                new_size = frappe.get_doc(
                                    {
                                        "doctype": "Velocetec Material Size",
                                        "material_form": "Bar",
                                        "d_dim": rm.get("d"),
                                        "l_dim": rm.get("l"),
                                        "name": str(rm.get("d"))
                                        + "x"
                                        + str(rm.get("l")),
                                    }
                                )
                                new_size.insert(ignore_permissions=True)
                                size_record = new_size.name
                        if size_record:
                            rm_row.custom_material_size = size_record
                        # --- End New Block for sub-child raw materials ---

                    # --- New Block: Process line_fixings for sub-child items ---
                    fixings = sub_child_vlc.get("line_fixings") or []
                    for fixing in fixings:
                        fixing_row = bom_creator.append("items", {})
                        fixing_row.fg_item = sub_child.get("part_number")
                        fixing_row.item_code = fixing.get("item") or "UNKNOWN"
                        fixing_row.item_name = fixing.get("item") or "UNKNOWN"
                        fixing_row.qty = fixing.get("qty") or 1
                        fixing_row.rate = fixing.get("costunit") or 0
                        fixing_row.custom_cost = fixing.get("costunit") or 0
                        fixing_row.uom = fixing.get("uom") or "Nos"
                        fixing_row.parent_row_no = str(sub_child_row_idx)
                        fixing_row.is_expandable = 0
                        fixing_row.do_not_explode = 1
                        fixing_row.sourced_by_supplier = 0
                        fixing_row.bom_created = 0
                    # --- End New Block for sub-child line_fixings ---

                # Check if there are deeper levels
                deeper_sub = False
                for x in vc_items:
                    if x.get("parent_part") == sub_child.get("part_number") and x.get(
                        "part_number"
                    ) != sub_child.get("part_number"):
                        deeper_sub = True
                        break
                if deeper_sub:
                    stack.append(
                        {"child": sub_child, "child_row_idx": sub_child_row_idx}
                    )

        # 8. Finalize BOM Creator
        bom_creator.save(ignore_permissions=True)
        created_bom_ids.append(bom_creator.name)

    # 9. Submit BOM Creators with delays to avoid deadlocks
    import time

    for i, bom_id in enumerate(created_bom_ids):
        if i > 0:
            time.sleep(5)  # 5 second delay between submissions

        bom_creator = frappe.get_doc("BOM Creator", bom_id)
        bom_creator.submit()
        frappe.db.commit()

    # 10. Return list of created BOM Creator IDs
    frappe.response["message"] = created_bom_ids


@frappe.whitelist()
def export_sales_order_worksheet(sales_order_name):
    doc = frappe.get_doc("Sales Order", sales_order_name)

    contact_person = ""
    if doc.contact_person:
        contact = frappe.get_doc("Contact", doc.contact_person)
        contact_person = f"{contact.first_name} {contact.last_name or ''}".strip()

    address = (
        frappe.get_doc("Address", doc.customer_address)
        if doc.customer_address
        else None
    )

    opportunity_desc = ""
    if doc.opportunity:
        opp = frappe.get_doc("Opportunity", doc.opportunity)
        opportunity_desc = opp.get("custom_opportunity_description", "")
        opportunity_number = doc.opportunity or ""

    # Load XLSX template (no VBA)
    template_path = os.path.join(
        frappe.get_site_path("public", "files", "Worksheet Template v2.xlsx")
    )
    wb = openpyxl.load_workbook(template_path)
    ws = wb.active

    # Fill fields
    ws["B2"] = doc.owner
    ws["C2"] = contact_person
    ws["D2"] = doc.customer_name

    if address:
        ws["E2"] = address.address_line1 or ""
        ws["F2"] = address.address_line2 or ""
        ws["H2"] = address.city or ""
        ws["I2"] = address.county or ""
        # ws["B8"] = address.state or ""
        ws["J2"] = address.country or ""
        ws["K2"] = address.pincode or ""

    ws["L2"] = doc.delivery_date.strftime("%d-%m-%Y") if doc.delivery_date else ""
    ws["M2"] = opportunity_desc
    ws["N2"] = doc.name
    ws["O2"] = opportunity_number

    # Save and attach as private file
    xlsx_io = BytesIO()
    wb.save(xlsx_io)
    xlsx_io.seek(0)

    file_name = f"{doc.name}-.xlsx"
    file_doc = save_file(
        file_name, xlsx_io.read(), "Sales Order", doc.name, is_private=True
    )
    return {"file_url": file_doc.file_url}


def _create_dimensioned_item_if_needed(rm, base_material_type, material_form=None):
    """Create dimensioned item with suffix if dimensions exist, similar to Material Cut"""
    # Generate dimensions string
    dimensions = _get_dimensions_string_from_rm(rm)

    if not dimensions:
        # No dimensions, return base material type
        return base_material_type

    # Remove existing dimensions from base material type to avoid duplication
    # Look for existing dimension patterns and remove them
    clean_base_material_type = _remove_existing_dimensions(base_material_type)

    # Create dimensioned item code with suffix using clean base name
    dimensioned_item_code = f"{clean_base_material_type} - {dimensions}"

    # Create Material Size if it doesn't exist
    _create_material_size_if_needed_from_rm(rm, dimensions)

    # Create dimensioned Item if it doesn't exist
    if not frappe.db.exists("Item", dimensioned_item_code):
        _create_dimensioned_item_from_rm(
            dimensioned_item_code, dimensions, base_material_type, material_form
        )
        frappe.msgprint(f"Item {dimensioned_item_code} created.")

    return dimensioned_item_code


def _remove_existing_dimensions(item_name):
    """Remove existing dimension suffixes from item name to avoid duplication"""
    import re

    # Pattern to match dimension suffixes like " - 38.0x28.0x18.0" or " - 19x14x9" or " - 25x100"
    # This matches patterns: " - {number}x{number}x{number}" or " - {number}x{number}"
    dimension_pattern = r"\s*-\s*\d+(?:\.\d+)?x\d+(?:\.\d+)?(?:x\d+(?:\.\d+)?)?$"

    # Remove the dimension suffix if it exists
    clean_name = re.sub(dimension_pattern, "", item_name).strip()

    return clean_name


def _get_dimensions_string_from_rm(rm):
    """Generate dimensions string from raw material dimensions"""
    if (
        rm.get("x") is not None
        and rm.get("y") is not None
        and rm.get("z") is not None
        and not (rm.get("d") or rm.get("l"))
    ):
        return f"{rm.get('x')}x{rm.get('y')}x{rm.get('z')}"
    elif (
        rm.get("d") is not None
        and rm.get("l") is not None
        and not (rm.get("x") or rm.get("y") or rm.get("z"))
    ):
        return f"{rm.get('d')}x{rm.get('l')}"
    return None


def _create_material_size_if_needed_from_rm(rm, dimensions):
    """Create Material Size if it doesn't exist"""
    if not frappe.db.exists("Velocetec Material Size", {"name": dimensions}):
        if (
            rm.get("x") is not None
            and rm.get("y") is not None
            and rm.get("z") is not None
        ):
            material_form = "Block"
            dimension_dict = {
                "x_dim": rm.get("x"),
                "y_dim": rm.get("y"),
                "z_dim": rm.get("z"),
            }
        elif rm.get("d") is not None and rm.get("l") is not None:
            material_form = "Bar"
            dimension_dict = {"d_dim": rm.get("d"), "l_dim": rm.get("l")}
        else:
            frappe.throw(f"Invalid dimensions for material {rm.get('material_type')}")

        new_size = frappe.get_doc(
            {
                "doctype": "Velocetec Material Size",
                "material_form": material_form,
                **dimension_dict,
            }
        )
        new_size.insert(ignore_permissions=True)


def _create_dimensioned_item_from_rm(
    dimensioned_item_code, dimensions, base_material_type, material_form=None
):
    """Create a new item with dimensions using deep copy to preserve all nested structures"""
    # Try to get base item, fallback to creating new if not exists
    base_item = None
    if frappe.db.exists("Item", base_material_type):
        base_item = frappe.get_doc("Item", base_material_type)

    if base_item:
        # Use deep copy to create a complete copy of the base item with all nested structures
        new_item_data = copy.deepcopy(base_item.as_dict())

        # Remove system fields that should not be copied
        system_fields_to_remove = [
            'name', 'creation', 'modified', 'modified_by', 'owner',
            'docstatus', 'idx', '_user_tags', '_comments', '_assign', '_liked_by'
        ]
        for field in system_fields_to_remove:
            new_item_data.pop(field, None)

        # Update with new item-specific values
        new_item_data.update({
            "doctype": "Item",
            "item_code": dimensioned_item_code,
            "item_name": dimensioned_item_code,
            "material_size": dimensions,
        })

        # Use the material form passed from VLC table context, fallback to base item's form
        if material_form:
            new_item_data["custom_material_form"] = material_form

        # Update description to include dimensions
        if base_item.description:
            new_item_data["description"] = f"{base_item.description} - {dimensions}"
        else:
            new_item_data["description"] = dimensioned_item_code

    else:
        # Fallback: Create minimal item data if no base item exists
        new_item_data = {
            "doctype": "Item",
            "item_code": dimensioned_item_code,
            "item_name": dimensioned_item_code,
            "item_group": "Raw Material",
            "stock_uom": "Nos",
            "is_sales_item": 1,
            "is_stock_item": 1,
            "has_batch_no": 1 if material_form in ["Bar", "Block"] else 0,
            "has_serial_no": 1 if material_form in ["Bar", "Block"] else 0,
            "custom_material_form": material_form,
            "material_size": dimensions,
            "description": dimensioned_item_code,
        }

    # Set the custom_material_size to link to the Velocetec Material Size record
    material_size_record = frappe.db.get_value("Velocetec Material Size", {"name": dimensions}, "name")
    if material_size_record:
        new_item_data["custom_material_size"] = material_size_record

    # Create the new item document from the deep-copied data
    new_item = frappe.get_doc(new_item_data)
    new_item.insert(ignore_permissions=True)

    # Create batch for the dimensioned item if batch tracking is enabled
    if new_item.has_batch_no:
        from velocetec.utils.batch_utils import create_batch_for_dimensioned_item

        # Get serial series from base item or new item
        serial_series = None
        if base_item:
            serial_series = base_item.get("serial_no_series")

        batch_id = create_batch_for_dimensioned_item(
            item_code=dimensioned_item_code,
            base_item_code=base_material_type,
            serial_no_series=serial_series
        )

        if not batch_id:
            frappe.log_error(
                message=f"Failed to create batch for dimensioned item {dimensioned_item_code}",
                title="Batch Creation Failed"
            )
