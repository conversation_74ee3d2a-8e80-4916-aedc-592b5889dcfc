import frappe
import pyqrcode
import io
import base64


@frappe.whitelist()
def generate_qrcode(qrcode_data):
    c = pyqrcode.create(qrcode_data)
    s = io.BytesIO()
    c.png(s, scale=3)
    encoded = "data:image/png;base64," + base64.b64encode(s.getvalue()).decode("ASCII")
    return encoded


@frappe.whitelist()
def get_item_receipt_details(item_code, material_size=None):
    """Get purchase receipt details for an item with optional material size filter"""

    conditions = ["pri.item_code = %(item_code)s"]
    values = {"item_code": item_code}

    if material_size:
        conditions.append("pri.material_size = %(material_size)s")
        values["material_size"] = material_size

    query = """
        SELECT
            pr.name as purchase_receipt,
            pr.posting_date,
            pr.supplier,
            pri.rate,
            pri.material_size
        FROM
            `tabPurchase Receipt` pr
        INNER JOIN
            `tabPurchase Receipt Item` pri ON pr.name = pri.parent
        WHERE
            pr.docstatus = 1
            AND {conditions}
        ORDER BY
            pr.posting_date DESC
    """.format(
        conditions=" AND ".join(conditions)
    )

    return frappe.db.sql(query, values, as_dict=True)


@frappe.whitelist()
def get_material_details(serial_no):
    """Get material details from serial number"""

    if not serial_no:
        frappe.throw("Serial number not found.")

    # Try the improved query first (for newer ERPNext versions with Serial and Batch Bundle)
    serial_no_details = frappe.db.sql(
        """
        SELECT
            sle.item_code,
            i.material_size as velocetec_material_size,
            sle.warehouse,
            sle.incoming_rate
        FROM
            `tabStock Ledger Entry` sle
        JOIN
            `tabSerial and Batch Bundle` sab
            ON sle.serial_and_batch_bundle = sab.name
        JOIN
            `tabSerial and Batch Entry` sbe
            ON sbe.parent = sab.name
        JOIN
            `tabItem` i
            ON i.item_code = sle.item_code    
        WHERE
            sbe.serial_no = %(serial_no)s
            AND sle.voucher_no = sab.voucher_no
            AND sab.type_of_transaction = 'Inward'
        ORDER BY
            sle.posting_date DESC, sle.posting_time DESC
        LIMIT 1
    """,
        {"serial_no": serial_no},
        as_dict=True,
    )

    if serial_no_details:
        return serial_no_details[0]

    # Fallback to older method if the above doesn't work
    sle = frappe.db.sql(
        """
        SELECT
            sle.item_code,
            sle.warehouse,
            sle.incoming_rate,
            i.material_size as velocetec_material_size
        FROM
            `tabStock Ledger Entry` sle
        JOIN
            `tabItem` i
            ON i.item_code = sle.item_code    
        WHERE
            serial_no = %(serial_no)s
            AND voucher_type = 'Purchase Receipt'
            AND actual_qty > 0
        ORDER BY
            posting_date DESC, posting_time DESC
        LIMIT 1
    """,
        {"serial_no": serial_no},
        as_dict=True,
    )

    if sle:
        return sle[0]
    else:
        # Final fallback to serial number document data
        if frappe.db.exists("Serial No", serial_no):
            serial_doc = frappe.get_doc("Serial No", serial_no)
            return {
                "item_code": serial_doc.item_code,
                "warehouse": serial_doc.warehouse,
                "incoming_rate": 0,
                "velocetec_material_size": None,
            }
        else:
            frappe.throw(
                "No matching Stock Ledger Entry found for the given Serial Number."
            )


@frappe.whitelist()
def calculate_target_material_rate(
    target_material_size, source_material_size, source_basic_rate, item_code
):
    """Calculate target material rate based on material size and dimensions"""

    target_material_size = (
        frappe.form_dict.get("target_material_size") or target_material_size
    )
    source_material_size = (
        frappe.form_dict.get("source_material_size") or source_material_size
    )
    source_basic_rate = frappe.utils.flt(
        frappe.form_dict.get("source_basic_rate") or source_basic_rate
    )
    item_code = frappe.form_dict.get("item_code") or item_code

    result = {}

    if not target_material_size or not source_basic_rate or not source_material_size:
        frappe.throw(
            "Target material size, source basic rate, and source material size are required."
        )

    target_material_size_details = frappe.get_doc(
        "Velocetec Material Size", target_material_size
    )
    material_form = target_material_size_details.material_form

    custom_price_per_cubic_meter = frappe.utils.flt(
        frappe.db.get_value(
            "Item", {"item_code": item_code}, "custom_price_per_cubic_meter"
        )
    )

    if material_form == "Block":
        x_dim = frappe.utils.flt(target_material_size_details.x_dim or 0)
        y_dim = frappe.utils.flt(target_material_size_details.y_dim or 0)
        z_dim = frappe.utils.flt(target_material_size_details.z_dim or 0)

        if x_dim > 0 and y_dim > 0 and z_dim > 0:
            target_basic_rate = (
                (custom_price_per_cubic_meter / 1000000000)
                * (x_dim + 10)
                * (y_dim + 10)
                * z_dim
            )
            result["target_basic_rate"] = frappe.utils.flt(target_basic_rate, 2)
        else:
            frappe.throw("Invalid dimensions for Block material.")

    elif material_form == "Bar":
        d_dim = frappe.utils.flt(target_material_size_details.d_dim or 0)
        l_dim = frappe.utils.flt(target_material_size_details.l_dim or 0)

        source_material_size_details = frappe.get_doc(
            "Velocetec Material Size", source_material_size
        )
        source_d_dim = frappe.utils.flt(source_material_size_details.d_dim or 0)
        source_l_dim = frappe.utils.flt(source_material_size_details.l_dim or 1)

        if source_d_dim > 0 and source_l_dim > 0 and d_dim > 0 and l_dim > 0:
            target_basic_rate = frappe.utils.flt(source_basic_rate) * (
                l_dim / source_l_dim
            )
            result["target_basic_rate"] = frappe.utils.flt(target_basic_rate, 2)
        else:
            frappe.throw("Invalid dimensions for Bar material.")

    else:
        frappe.throw("Unsupported material form: {0}".format(material_form))

    return result
