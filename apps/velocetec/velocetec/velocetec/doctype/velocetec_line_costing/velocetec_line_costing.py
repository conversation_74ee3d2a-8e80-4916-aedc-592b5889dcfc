# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import json
import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import ceil


@frappe.whitelist()
def simple_rollback(docname):
    """Simple server-side method to rollback the current transaction"""
    try:
        # Check if the parent VC is linked
        vlc = frappe.get_doc("Velocetec Line Costing", docname)
        if vlc.velocetec_costing:
            link_status = frappe.db.get_value(
                "Velocetec Costing", vlc.velocetec_costing, "quotation_link_status"
            )
            if link_status != "Linked":
                return {
                    "success": False,
                    "message": "Parent VC is not linked to a quotation.",
                }

        # Rollback the current transaction
        frappe.db.rollback()

        # Return success
        return {"success": True}
    except Exception as e:
        frappe.log_error(
            f"Error in VLC simple rollback: {str(e)}", "VLC Rollback Error"
        )
        return {"success": False}


@frappe.whitelist()
def get_vc_part_number(velocetec_costing, velocetec_costing_detail):
    """Get the part number from the Velocetec Costing Detail"""
    try:
        if not velocetec_costing or not velocetec_costing_detail:
            return {"success": False, "message": "Missing required parameters"}

        if not frappe.db.exists(
            "Velocetec Costing Detail",
            {"name": velocetec_costing_detail, "parent": velocetec_costing},
        ):
            return {"success": False, "message": "Velocetec Costing Detail not found"}

        part_number = frappe.db.get_value(
            "Velocetec Costing Detail", velocetec_costing_detail, "part_number"
        )

        return {"success": True, "part_number": part_number}
    except Exception as e:
        frappe.log_error(
            f"Error getting VC part number: {str(e)}", "VLC Part Number Error"
        )
        return {"success": False, "message": f"Error: {str(e)}"}


class VelocetecLineCosting(Document):
    def before_insert(self):
        # Check if this is a duplication process by looking at the velocetec_costing_detail
        if self.velocetec_costing_detail and not frappe.db.exists(
            "Velocetec Costing Detail", self.velocetec_costing_detail
        ):
            # This is likely a duplication process, set flag to skip validation
            self._skip_validation = True

    def validate(self):
        """Validate and calculate costs for Velocetec Line Costing."""

        for row in self.line_fixings:
            if row.item:
                if not frappe.db.exists("Item", row.item):
                    frappe.throw(_("Item {} does not exist".format(row.item)))

        # Skip validation if this is a duplication process
        if hasattr(self, "_skip_validation") and self._skip_validation:
            return

        # Check if the referenced VCD exists, if not, skip validation
        if self.velocetec_costing_detail and not frappe.db.exists(
            "Velocetec Costing Detail", self.velocetec_costing_detail
        ):
            return

        # 0) Prevent changes if parent VC is already linked
        link_status = frappe.db.get_value(
            "Velocetec Costing", self.velocetec_costing, "quotation_link_status"
        )
        if link_status == "Linked" and not frappe.flags.get("allow_linked_vlc_changes"):
            frappe.throw(
                _(
                    "This VLC's parent VC is linked to a quotation. Changes are not allowed. "
                    "Use the 'Rollback Changes' button to discard your changes."
                )
            )

        # 1) Figure out how many loads we need
        if self.get("local_qty"):
            qty = int(self.local_qty)
        else:
            qty = (
                frappe.db.get_value(
                    "Velocetec Costing Detail",
                    self.velocetec_costing_detail,
                    "quantity",
                )
                or 0
            )

        if (self.inspection_percent or 0) > 100:
            frappe.throw(_("Inspection percent cannot be greater than 100%"))

        self.no_of_loads = ceil(qty / (self.qtyload or 1))

        # 2) Accumulators
        total_block = 0.0
        total_bar = 0.0
        total_material_price = 0.0
        total_fixings_cost = 0.0

        # 3) Block material
        for row in self.line_material_details or []:
            if row.x and row.y and row.z:
                # cost per mm³
                cppm = (row.cost_per_cubic_m or 0) / 1e9
                if not row.amount:
                    volume = (row.x + 10) * (row.y + 10) * row.z
                    row.amount = cppm * volume
                markup_percent = float(row.markup_percent or 0)
                row.price = row.amount * (1 + markup_percent / 100)

                total_block += row.price  # Changed from row.amount to row.price
                total_material_price += row.price

        # 4) Bar material
        for row in self.bar_material_details or []:
            if row.d and row.l:
                cppm = (row.cost_per_cubic_m or 0) / 1e9
                if not row.amount:
                    r = row.d / 2
                    volume = 3.14159265359 * (r**2) * row.l
                    row.amount = cppm * volume
                markup_percent = float(row.markup_percent or 0)
                row.price = row.amount * (1 + markup_percent / 100)

                total_bar += row.price  # Changed from row.amount to row.price
                total_material_price += row.price

        # 5) Fixings
        for row in self.line_fixings or []:
            row.total_cost = (row.qty or 0) * (row.costunit or 0)
            markup_percent = float(row.markup_percent or 0)
            row.price = row.total_cost * (1 + markup_percent / 100)
            total_fixings_cost += row.price

        # 6) Routing & workstations
        cost_group = {}
        raw_cost_group = {}
        for row in self.routing_material_details or []:
            hrs = row.hrs or 0
            rate = row.rate_hr or 0

            # a) default cost
            if row.workstation_type not in (
                "Subcontract",
                "Inspection",
                "Mass Finishing",
            ):
                row.cost_each = hrs * rate

            # b) inspection override
            elif row.workstation_type == "Inspection" and not row.is_fixed:
                if qty <= 1:
                    insp_qty = 1
                else:
                    comp = int(qty * ((self.inspection_percent or 0) / 100))
                    min_q = self.minimum_inspection_qty or 0
                    min_q = qty if min_q > qty else min_q
                    insp_qty = max(comp, min_q) if min_q and comp < min_q else comp

                row.cost_each = hrs * rate
                row.inspection_qty = insp_qty
                row.apply_qty = frappe.db.get_value(
                    "Operation", row.operation, "apply_actual_process_qty"
                )

            # c) mass‑finishing override
            elif row.workstation_type == "Mass Finishing":
                row.cost_each = hrs * rate
                row.mass_finishing_loads = self.no_of_loads
                row.apply_qty = frappe.db.get_value(
                    "Operation", row.operation, "apply_actual_process_qty"
                )

            # d) apply markup & accumulate
            markup_percent = float(row.markup_percent or 0)
            row.price = row.cost_each * (1 + markup_percent / 100)
            key = (row.workstation_type, row.is_fixed)
            cost_group[key] = cost_group.get(key, 0) + row.price
            raw_cost_group[key] = raw_cost_group.get(key, 0) + row.cost_each

        # 7) Build summary_details and compute per‑workstation totals
        self.summary_details = []
        workstation_totals = {}
        raw_workstation_totals = {}
        for (ws_type, is_fixed), cost in cost_group.items():
            totals = workstation_totals.setdefault(
                ws_type, {"fixed": 0.0, "variable": 0.0}
            )
            totals["fixed" if is_fixed else "variable"] += cost

            # Also track raw costs without markup
            raw_totals = raw_workstation_totals.setdefault(
                ws_type, {"fixed": 0.0, "variable": 0.0}
            )
            raw_totals["fixed" if is_fixed else "variable"] += raw_cost_group.get(
                (ws_type, is_fixed), 0
            )

            new_row = self.append("summary_details", {})
            new_row.workstation_type = ws_type
            new_row.is_fixed = is_fixed
            new_row.total_cost = cost

        # 8) Compute final per‑piece costs
        final_totals = {}
        raw_final_totals = {}
        for ws_type, t in workstation_totals.items():
            f = t["fixed"]
            v = t["variable"]
            final_totals[ws_type] = (v * max(qty, 1) + f) / max(qty, 1)

            # Calculate raw costs without markup
            raw_t = raw_workstation_totals.get(ws_type, {"fixed": 0.0, "variable": 0.0})
            raw_f = raw_t["fixed"]
            raw_v = raw_t["variable"]
            raw_final_totals[ws_type] = (raw_v * max(qty, 1) + raw_f) / max(qty, 1)

        # 9) Write everything back onto the document
        self.block_material_amount = total_block
        self.bar_material_amount = total_bar
        self.material_price = total_material_price
        self.total_fixings_cost = total_fixings_cost

        # workstation fields
        self.machining = final_totals.get("Machining", 0)
        self.finishing = final_totals.get("Finishing", 0)

        # Calculate inspection cost
        inspection_fixed = 0
        inspection_variable = 0

        for (ws_type, is_fixed), cost in cost_group.items():
            if ws_type == "Inspection":
                if is_fixed:
                    inspection_fixed += cost
                else:
                    inspection_variable += cost

        inspection_qty = 0
        apply_inspection_qty = False

        for row in self.routing_material_details or []:
            if row.workstation_type == "Inspection" and not row.is_fixed:
                if hasattr(row, "inspection_qty"):
                    inspection_qty = row.inspection_qty
                    apply_inspection_qty = getattr(row, "apply_qty", False)
                    break

        if apply_inspection_qty:
            inspection_variable = inspection_variable * inspection_qty

        total_inspection_cost = inspection_fixed + inspection_variable
        self.inspection = total_inspection_cost / max(qty, 1)

        # Calculate mass finishing cost
        mass_finishing_fixed = 0
        mass_finishing_variable = 0

        for (ws_type, is_fixed), cost in cost_group.items():
            if ws_type == "Mass Finishing":
                if is_fixed:
                    mass_finishing_fixed += cost
                else:
                    mass_finishing_variable += cost

        mass_finishing_loads = 0
        apply_mass_finishing_loads = False

        for row in self.routing_material_details or []:
            if row.workstation_type == "Mass Finishing":
                if hasattr(row, "mass_finishing_loads"):
                    mass_finishing_loads = row.mass_finishing_loads
                    apply_mass_finishing_loads = getattr(row, "apply_qty", False)
                    break

        if apply_mass_finishing_loads:
            mass_finishing_variable = mass_finishing_variable * mass_finishing_loads

        total_mass_finishing_cost = mass_finishing_fixed + mass_finishing_variable
        self.mass_finishing = total_mass_finishing_cost / max(qty, 1)

        self.other = final_totals.get("Other", 0)
        self.sub_con = final_totals.get("Subcontract", 0)
        self.tool_making = final_totals.get("Tool Making", 0)
        self.turning = final_totals.get("Turning", 0)
        self.design = final_totals.get("Design", 0)
        self.edm = final_totals.get("EDM", 0)
        self.assembly = final_totals.get("Assembly", 0)

        # Calculate raw costs for Velocetec Costing Detail
        raw_machining = raw_final_totals.get("Machining", 0)
        raw_finishing = raw_final_totals.get("Finishing", 0)

        # Calculate raw inspection cost
        raw_inspection_fixed = 0
        raw_inspection_variable = 0

        for (ws_type, is_fixed), cost in raw_cost_group.items():
            if ws_type == "Inspection":
                if is_fixed:
                    raw_inspection_fixed += cost
                else:
                    raw_inspection_variable += cost

        if apply_inspection_qty:
            raw_inspection_variable = raw_inspection_variable * inspection_qty

        total_raw_inspection_cost = raw_inspection_fixed + raw_inspection_variable
        raw_inspection = total_raw_inspection_cost / max(qty, 1)

        # Calculate raw mass finishing cost
        raw_mass_finishing_fixed = 0
        raw_mass_finishing_variable = 0

        for (ws_type, is_fixed), cost in raw_cost_group.items():
            if ws_type == "Mass Finishing":
                if is_fixed:
                    raw_mass_finishing_fixed += cost
                else:
                    raw_mass_finishing_variable += cost

        if apply_mass_finishing_loads:
            raw_mass_finishing_variable = (
                raw_mass_finishing_variable * mass_finishing_loads
            )

        total_raw_mass_finishing_cost = (
            raw_mass_finishing_fixed + raw_mass_finishing_variable
        )
        raw_mass_finishing = total_raw_mass_finishing_cost / max(qty, 1)

        raw_other = raw_final_totals.get("Other", 0)
        raw_sub_con = raw_final_totals.get("Subcontract", 0)
        raw_tool_making = raw_final_totals.get("Tool Making", 0)
        raw_turning = raw_final_totals.get("Turning", 0)
        raw_design = raw_final_totals.get("Design", 0)
        raw_edm = raw_final_totals.get("EDM", 0)
        raw_assembly = raw_final_totals.get("Assembly", 0)

        # Calculate raw material costs (without markup)
        # Note: We still use the raw costs (without markup) for raw_material_cost
        # This is used for sell_cost_each which should be based on costs without markup
        raw_material_cost = sum(
            row.amount
            for row in self.line_material_details or []
            if row.x and row.y and row.z
        ) + sum(
            row.amount for row in self.bar_material_details or [] if row.d and row.l
        )
        raw_fixings_cost = sum(
            (row.qty or 0) * (row.costunit or 0) for row in self.line_fixings or []
        )

        # Calculate total raw cost - using the same logic as sell_price_each
        # For Inspection and Mass Finishing, we need to use the raw_inspection and raw_mass_finishing
        # values that have already been calculated with the special handling
        raw_total_cost = sum(
            [
                raw_material_cost,
                raw_machining,
                raw_design,
                raw_finishing,
                raw_inspection,  # This already has the special inspection qty handling
                raw_other,
                raw_mass_finishing,  # This already has the special mass finishing loads handling
                raw_sub_con,
                raw_fixings_cost,
                raw_tool_making,
                raw_turning,
                raw_edm,
                raw_assembly,
            ]
        )

        # Note: delivery_each is not a field in VLC, but is in Velocetec Costing Detail
        # We need to get it from the parent Velocetec Costing Detail
        delivery_each = 0

        try:
            # Check if the Velocetec Costing Detail exists before trying to get it
            if frappe.db.exists(
                "Velocetec Costing Detail", self.velocetec_costing_detail
            ):
                vcd = frappe.get_doc(
                    "Velocetec Costing Detail", self.velocetec_costing_detail
                )
                delivery_each = (
                    vcd.delivery_each if hasattr(vcd, "delivery_each") else 0
                )

                # Add delivery cost to the raw total - this is a raw cost without markup
                raw_total_cost += delivery_each

                # Persist to the Velocetec Costing Detail
                frappe.db.set_value(
                    "Velocetec Costing Detail",
                    self.velocetec_costing_detail,
                    {
                        "velocetec_line_costing": json.dumps(
                            self.as_dict(no_nulls=1), default=str
                        ),
                        "sell_cost_each": raw_total_cost,
                        "own_sell_cost_each": raw_total_cost,
                        "material_price": self.material_price,
                        "machining": self.machining,
                        "finishing": self.finishing,
                        "inspection": self.inspection,
                        "mass_finishing": self.mass_finishing,
                        "other": self.other,
                        "sub_con": self.sub_con,
                        "tool_making": self.tool_making,
                        "turning": self.turning,
                        "design": self.design,
                        "edm": self.edm,
                        "assembly": self.assembly,
                        "fixings": self.total_fixings_cost,
                        "internal_notes":   self.internal_notes,
                        "external_notes": self.external_notes,
                    },
                )

                # Also update the parent VC to trigger recalculation
                frappe.db.commit()
        except Exception as e:
            # Log the error but don't stop the process
            frappe.log_error(
                "VLC Validation Error",
                f"Error in VLC validation for {self.name}: {str(e)}",
            )
