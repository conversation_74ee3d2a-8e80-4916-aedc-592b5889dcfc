// Copyright (c) 2025, Sydney Kibanga and contributors
// For license information, please see license.txt

frappe.ui.form.on('Velocetec Line Costing', {
    onload: function (frm) {
        // Set up query for operations
        frm.set_query("operation", "routing_material_details", function (doc, cdt, cdn) {
            const child = locals[cdt][cdn];
            return {
                filters: {
                    workstation_type: child.workstation_type
                }
            };
        });

        frm.set_query("item", "line_fixings", function () {
            return {
                filters: {
                    include_item_in_manufacturing: 1
                }
            };
        });

        if (frm.doc.velocetec_costing && frm.doc.velocetec_costing_detail) {
            frappe.call({
                method: "velocetec.velocetec.doctype.velocetec_line_costing.velocetec_line_costing.get_vc_part_number",
                args: {
                    velocetec_costing: frm.doc.velocetec_costing,
                    velocetec_costing_detail: frm.doc.velocetec_costing_detail
                },
                callback: function (r) {
                    if (r.message && r.message.success && r.message.part_number) {
                        const vc_part_number = r.message.part_number;

                        if (frm.doc.part_number !== vc_part_number) {
                            frm.set_value("part_number", vc_part_number);
                        }
                    }
                }
            });
        }
    },

    refresh: function (frm) {
        // Handle "This form has been modified" message
        if (frm.doc.__conflict_resolution) {
            // If we're in conflict resolution mode, reload the document
            frm.reload_doc();

            // Show a message to the user
            frappe.show_alert({
                message: __("The form has been refreshed to resolve conflicts"),
                indicator: "blue"
            }, 5);

            // Clear the conflict resolution flag
            frm.doc.__conflict_resolution = false;
        }

        // Check if parent VC is linked to a quotation
        if (frm.doc.velocetec_costing) {
            frappe.call({
                method: "frappe.client.get_value",
                args: {
                    doctype: "Velocetec Costing",
                    filters: { name: frm.doc.velocetec_costing },
                    fieldname: "quotation_link_status"
                },
                callback: function (r) {
                    if (r.message && r.message.quotation_link_status === "Linked") {
                        // Add Rollback Changes button
                        frm.add_custom_button(__("Rollback Changes"), function () {
                            frappe.confirm(
                                __("This will discard all unsaved changes. Continue?"),
                                function () {
                                    // Simple rollback using frappe.db.rollback()
                                    frappe.call({
                                        method: "velocetec.velocetec.doctype.velocetec_line_costing.velocetec_line_costing.simple_rollback",
                                        args: {
                                            docname: frm.doc.name
                                        },
                                        freeze: true,
                                        freeze_message: __("Rolling back changes..."),
                                        callback: function () {
                                            frappe.show_alert({
                                                message: __("Changes rolled back successfully."),
                                                indicator: "green"
                                            }, 3);
                                            frm.reload_doc();
                                        }
                                    });
                                }
                            );
                        }, __("Actions"));
                    }
                }
            });

            // Ensure part number matches the one in VC line item
            if (frm.doc.velocetec_costing_detail) {
                frappe.call({
                    method: "velocetec.velocetec.doctype.velocetec_line_costing.velocetec_line_costing.get_vc_part_number",
                    args: {
                        velocetec_costing: frm.doc.velocetec_costing,
                        velocetec_costing_detail: frm.doc.velocetec_costing_detail
                    },
                    callback: function (r) {
                        if (r.message && r.message.success && r.message.part_number) {
                            const vc_part_number = r.message.part_number;

                            // If part numbers don't match, update the VLC part number
                            if (frm.doc.part_number !== vc_part_number) {
                                frappe.show_alert({
                                    message: __(`Updating part number from "${frm.doc.part_number}" to "${vc_part_number}"`),
                                    indicator: "blue"
                                }, 5);

                                frm.set_value("part_number", vc_part_number);

                                // Save the document to persist the change
                                setTimeout(() => {
                                    if (frm.doc.__unsaved) {
                                        frm.save().then(() => {
                                            frappe.show_alert({
                                                message: __("Part number synchronized with Velocetec Costing"),
                                                indicator: "green"
                                            }, 3);
                                        });
                                    }
                                }, 1000);
                            }
                        }
                    }
                });
            }
        }
    },

    validate: function (frm) {
        // If there's a conflict, set a flag to handle it on refresh
        if (frm.doc.__modified_conflict) {
            frm.doc.__conflict_resolution = true;
        }
    },
    return_to_costing: async function (frm) {
        // Show a loading indicator
        frappe.show_alert({
            message: __("Preparing to return to costing..."),
            indicator: "blue"
        }, 3);

        try {
            if (frm.doc.velocetec_costing) {
                // Check if parent VC is linked to a quotation
                const vc_status = await frappe.call({
                    method: "frappe.client.get_value",
                    args: {
                        doctype: "Velocetec Costing",
                        filters: { name: frm.doc.velocetec_costing },
                        fieldname: "quotation_link_status"
                    }
                });

                const is_linked = vc_status.message && vc_status.message.quotation_link_status === "Linked";

                // If the document is linked and has unsaved changes, ask user if they want to discard changes
                if (is_linked && frm.doc.__unsaved) {
                    frappe.confirm(
                        __("This document is linked to a quotation. Your changes will be discarded. Continue?"),
                        function () {
                            // User confirmed, discard changes and navigate to parent VC
                            frm.doc.__unsaved = 0;
                            frappe.set_route("Form", "Velocetec Costing", frm.doc.velocetec_costing);
                        }
                    );
                    return;
                }

                // For non-linked documents or linked documents without changes, proceed normally
                if (!is_linked && frm.doc.__unsaved) {
                    // Save the document first to ensure all changes are persisted
                    await frm.save();
                }

                // Get a fresh copy of the document to avoid conflicts
                await frm.reload_doc();

                // Use the server-side method to save and validate the document
                const result = await frappe.call({
                    method: "velocetec.api.velocetec_line_costing.save_and_validate_vlc",
                    args: {
                        doc_data: frm.doc
                    },
                    freeze: true,
                    freeze_message: __("Updating costs..."),
                });

                if (result && result.message && result.message.success) {
                    // Clear any unsaved flag
                    frm.doc.__unsaved = 0;

                    // Set up a route hook to reload the document after navigation
                    frappe.route_hooks.after_load = function (vc_frm) {
                        // Make sure we're on the correct form
                        if (vc_frm.doctype === "Velocetec Costing" && vc_frm.docname === frm.doc.velocetec_costing) {
                            // Remove from locals to force a fresh fetch from server
                            frappe.model.remove_from_locals("Velocetec Costing", frm.doc.velocetec_costing);

                            // Clear all child items from locals to ensure fresh data
                            (vc_frm.doc.items || []).forEach(item => {
                                if (item.name) {
                                    frappe.model.remove_from_locals("Velocetec Costing Detail", item.name);
                                }
                            });

                            // Reload the document
                            vc_frm.reload_doc().then(() => {
                                // Force recalculation after reload
                                if (typeof runCostingCalculation === 'function') {
                                    runCostingCalculation(vc_frm, true);
                                }

                                frappe.show_alert({
                                    message: __("Velocetec Costing refreshed with updated values"),
                                    indicator: "green"
                                }, 3);
                            });
                        }
                    };

                    // Navigate to the Velocetec Costing form
                    frappe.set_route("Form", "Velocetec Costing", frm.doc.velocetec_costing);
                } else {
                    // Show error message
                    frappe.msgprint({
                        title: __("Error"),
                        indicator: 'red',
                        message: result.message?.message || __("Failed to save and update costs.")
                    });
                }
            } else {
                frappe.msgprint(__("No linked Velocetec Costing found."));
            }
        } catch (error) {
            console.error("Error in return_to_costing:", error);
            frappe.msgprint({
                title: __("Error"),
                indicator: 'red',
                message: __("Failed to update costs. Please try again.")
            });
        }
    },


    routing_template: function (frm) {
        if (frm.doc.routing_template) {
            frm.clear_table('routing_material_details');

            frappe.call({
                method: "frappe.client.get",
                args: {
                    doctype: "Routing",
                    name: frm.doc.routing_template
                },
                callback: function (r) {
                    if (!r.exc && r.message) {
                        let routing = r.message;
                        let operations = routing.operations;

                        if (operations && operations.length > 0) {
                            operations.forEach(function (op) {
                                let child = frm.add_child('routing_material_details');

                                child.workstation_type = op.workstation_type || '';
                                child.operation = op.operation || '';
                                child.is_fixed = op.fixed_time === 1 ? 1 : 0; // Assuming 'fixed_time' indicates if operation is fixed
                                child.qty = frm.doc.batch_size || 1; // Assuming batch_size is defined at parent level
                                child.hrs = (op.time_in_mins || 0) / 60;
                                child.rate_hr = op.hour_rate || 0;
                                child.markup_percent = 0;
                            });

                            frm.refresh_field('routing_material_details');

                            frappe.show_alert({
                                message: __('Operations populated successfully.'),
                                indicator: 'green'
                            });
                        } else {
                            frappe.msgprint(__('No operations found in the selected Routing Template.'));
                        }
                    } else {
                        frappe.msgprint(__('Failed to fetch the Routing Template.'));
                    }
                }
            });
        }
    },

    qtyload: function (frm) {
        calculateLineCosts(frm);
    },
});

frappe.ui.form.on("Velocetec Line Bar Material Detail", {
    d: function (frm, cdt, cdn) {
        row = locals[cdt][cdn];
        if (row.d && row.l) {
            calculateLineCosts(frm);
        }
    },
    l: function (frm, cdt, cdn) {
        row = locals[cdt][cdn];
        if (row.d && row.l) {
            calculateLineCosts(frm);
        }
    },
    amount: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    markup_percent: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },

    // Fire if a row is added or removed in bar_material_details
    bar_material_details_add: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    bar_material_details_remove: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    }
});

frappe.ui.form.on("Velocetec Line Block Material Detail", {
    x: function (frm, cdt, cdn) {
        row = locals[cdt][cdn];
        if (row.x && row.y && row.z) {
            calculateLineCosts(frm);
        }
    },
    y: function (frm, cdt, cdn) {
        row = locals[cdt][cdn];
        if (row.x && row.y && row.z) {
            calculateLineCosts(frm);
        }
    },
    z: function (frm, cdt, cdn) {
        row = locals[cdt][cdn];
        if (row.x && row.y && row.z) {
            calculateLineCosts(frm);
        }
    },
    amount: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    markup_percent: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },

    // If a row is added or removed in line_material_details
    line_material_details_add: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    line_material_details_remove: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    }
});

frappe.ui.form.on("Velocetec Line Fixing Detail", {
    qty: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    costunit: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    markup_percent: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    amount: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },

    // If a row is added or removed in line_fixings
    line_fixings_add: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    },
    line_fixings_remove: function (frm, cdt, cdn) {
        calculateLineCosts(frm);
    }
});

frappe.ui.form.on("Velocetec Line Routing Detail", {
    workstation_type: function (frm, cdt, cdn) {
        const child = locals[cdt][cdn];
        // Trigger field refresh to apply new query
        frappe.model.set_value(cdt, cdn, 'operation', '');
        calculateLineCosts(frm);
    },
    operation: function (frm, cdt, cdn) {
        const child = locals[cdt][cdn];

        if (child.operation) {
            // Get the workstation from the operation
            frappe.call({
                method: "frappe.client.get",
                args: {
                    doctype: "Operation",
                    name: child.operation
                },
                callback: function (r) {
                    if (r.message && r.message.workstation) {
                        // Get the hour_rate from the workstation
                        frappe.call({
                            method: "frappe.client.get",
                            args: {
                                doctype: "Workstation",
                                name: r.message.workstation
                            },
                            callback: function (ws_r) {
                                if (ws_r.message && ws_r.message.hour_rate) {
                                    frappe.model.set_value(cdt, cdn, 'rate_hr', ws_r.message.hour_rate);
                                    frappe.show_alert({
                                        message: __(`Rate/hr updated from workstation ${r.message.workstation}`),
                                        indicator: 'green'
                                    }, 3);
                                }
                                calculateLineCosts(frm);
                            }
                        });
                    } else {
                        calculateLineCosts(frm);
                    }
                }
            });
        } else {
            calculateLineCosts(frm);
        }
    },
    hrs: function (frm) {
        calculateLineCosts(frm);
    },
    rate_hr: function (frm) {
        calculateLineCosts(frm);
    },
    is_fixed: function (frm) {
        calculateLineCosts(frm);
    },

    markup_percent: function (frm) {
        calculateLineCosts(frm);
    },

    // If a row is added or removed in routing_material_details
    routing_material_details_add: function (frm) {
        calculateLineCosts(frm);
    },
    routing_material_details_remove: function (frm) {
        calculateLineCosts(frm);
    }
});
frappe.ui.form.on("Summary Details", {
    workstation_type: function (frm) {
        calculateLineCosts(frm);
    },
    is_fixed: function (frm) {
        calculateLineCosts(frm);
    },
    total_cost: function (frm) {
        calculateLineCosts(frm);
    },

    // If a row is added or removed in summary_details
    summary_details_add: function (frm) {
        calculateLineCosts(frm);
    },
    summary_details_remove: function (frm) {
        calculateLineCosts(frm);
    }
});



// Debounce function to prevent multiple rapid calls
const debounce = (func, delay) => {
    let debounceTimer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => func.apply(context, args), delay);
    };
};

// Create a debounced version of the calculation function
const debouncedCalculation = debounce(async function (frm, caller = null) {
    await _calculateLineCosts(frm, caller);
}, 500); // 500ms delay

// Flag to track if calculation is in progress
let calculationInProgress = false;

async function calculateLineCosts(frm, caller = null) {
    // Use the debounced version to prevent multiple rapid calls
    debouncedCalculation(frm, caller);
}

async function _calculateLineCosts(frm, caller = null) {
    // If calculation is already in progress, don't start another one
    if (calculationInProgress) return;

    // Set flag to indicate calculation is in progress
    calculationInProgress = true;

    try {
        // Use get_list to fetch the parent Velocetec Costing's docstatus
        let vc_status_response = await frappe.call({
            method: "frappe.client.get_list",
            args: {
                doctype: "Velocetec Costing",
                filters: { name: frm.doc.velocetec_costing },
                fields: ["docstatus", "quotation_link_status"],
                limit_page_length: 1
            }
        });

        // Check if we got a valid response
        if (vc_status_response && vc_status_response.message && vc_status_response.message.length > 0) {
            let quotation_link_status = vc_status_response.message[0].quotation_link_status;

            // If the parent VC is Linked, bypass recalculation
            if (quotation_link_status == "Linked") {
                frappe.show_alert({
                    message: __("This VC is Linked; recalculation is disabled."),
                    indicator: "blue"
                }, 3);
                calculationInProgress = false;
                return;
            }
        }
    } catch (error) {
        console.error("Error checking VC status:", error);
        calculationInProgress = false;
        return;
    }

    let quantity = 0;

    // 1. Fetch the main Velocetec Costing doc
    if (frm.doc.velocetec_costing) {
        const vc_result = await frappe.call({
            method: "frappe.client.get",
            args: {
                doctype: "Velocetec Costing",
                name: frm.doc.velocetec_costing
            }
        });

        if (vc_result && vc_result.message) {
            // vc_doc is the main Velocetec Costing document
            let vc_doc = vc_result.message;

            // 2. Look through vc_doc.items (child table) to find the specific row
            //    that matches frm.doc.velocetec_costing_detail
            let child_row = (vc_doc.items || []).find(item => {
                return item.name === frm.doc.velocetec_costing_detail;
            });

            if (child_row) {
                // 3. Suppose that child_row has a field "quantity"
                //    Use it in your calculations:
                quantity = child_row.quantity || 0;
            }
        }
    }

    let block_material_amount = 0;
    let bar_material_amount = 0;
    let total_fixings_cost = 0;
    let total_material_price = 0;

    // Raw cost accumulators (without markup)
    let raw_block_material_amount = 0;
    let raw_bar_material_amount = 0;
    let raw_fixings_cost = 0;

    let no_of_loads = Math.ceil((quantity || 0) / (frm.doc.qtyload || 1));
    if (!caller) {
        frm.set_value("no_of_loads", no_of_loads);
    }

    (frm.doc.line_material_details || []).forEach(row => {
        if (row.x && row.y && row.z) {
            if (!row.amount) {
                let cost_per_cubic_mm = (row.cost_per_cubic_m || 0) / **********;
                let cubic_mm_required = (row.x + 10) * (row.y + 10) * row.z;
                row.amount = cost_per_cubic_mm * cubic_mm_required;
            }

            row.price = row.amount * (1 + (row.markup_percent || 0) / 100);
            block_material_amount += row.price; // Changed from row.amount to row.price
            total_material_price += row.price;

            // Track raw costs without markup
            raw_block_material_amount += row.amount;
        }
    });

    (frm.doc.bar_material_details || []).forEach(row => {
        if (row.d && row.l) {
            if (!row.amount) {
                let radius_mm = row.d / 2;
                let cubic_mm_required = 3.14159265359 * Math.pow(radius_mm, 2) * row.l;
                let cost_per_cubic_mm = (row.cost_per_cubic_m || 0) / **********;
                row.amount = cost_per_cubic_mm * cubic_mm_required;
            }
            row.price = row.amount * (1 + (row.markup_percent || 0) / 100);

            bar_material_amount += row.price; // Changed from row.amount to row.price
            total_material_price += row.price;

            // Track raw costs without markup
            raw_bar_material_amount += row.amount;
        }
    });

    (frm.doc.routing_material_details || []).forEach(row => {
        if (row.workstation_type === "Subcontract") return;
        row.cost_each = (row.hrs || 0) * (row.rate_hr || 0);
    });

    (frm.doc.line_fixings || []).forEach(row => {
        let raw_cost = (row.qty || 0) * (row.costunit || 0);
        row.price = raw_cost;
        row.total_cost = raw_cost * (1 + (row.markup_percent || 0) / 100);
        total_fixings_cost += row.total_cost;

        // Track raw costs without markup
        raw_fixings_cost += raw_cost;
    });

    frm.clear_table("summary_details");
    let cost_group = {};
    let raw_cost_group = {};
    (frm.doc.routing_material_details || []).forEach(row => {
        let wt = row.workstation_type || "";
        let is_fixed = row.is_fixed || 0;
        let cost = row.cost_each || 0;
        let key = wt + "_" + is_fixed;

        row.price = (row.cost_each || 0) * (1 + (row.markup_percent || 0) / 100);

        if (!row.markup_percent) {
            row.price = row.cost_each
        }

        cost_group[key] = (cost_group[key] || 0) + row.price;
        raw_cost_group[key] = (raw_cost_group[key] || 0) + cost;
    });

    for (let key in cost_group) {
        let [wt, is_fixed] = key.split("_");
        let new_row = frappe.model.add_child(frm.doc, "Summary Details", "summary_details");
        new_row.workstation_type = wt;
        new_row.is_fixed = parseInt(is_fixed, 10);
        new_row.total_cost = cost_group[key];
    }

    cost_group = {};
    (frm.doc.routing_material_details || []).forEach(row => {
        let wt = row.workstation_type || "";
        let is_fixed = row.is_fixed || 0;
        let cost = row.cost_each || 0;

        row.price = cost * (1 + (row.markup_percent / 100));
        if (!row.markup_percent) {
            row.price = row.cost_each
        }

        if (wt === "Inspection" && !is_fixed) {
            let inspection_qty = 1;
            if ((quantity || 0) > 1) {
                let computed_inspections = Math.ceil((quantity || 0) * ((frm.doc.inspection_percent || 0) / 100));
                if (frm.doc.minimum_inspection_qty && computed_inspections < frm.doc.minimum_inspection_qty) {
                    if (quantity < frm.doc.minimum_inspection_qty) {
                        inspection_qty = quantity;
                    } else {
                        inspection_qty = frm.doc.minimum_inspection_qty;
                    }
                } else {
                    inspection_qty = computed_inspections;
                }
            }
            cost = cost * inspection_qty;
        }

        if (wt === "Mass Finishing") {
            cost = ((no_of_loads || 0) * (row.hrs || 0) * (row.rate_hr || 0)) / (quantity || 1);
        }

        let key = wt + "_" + is_fixed;
        cost_group[key] = (cost_group[key] || 0) + cost;
    });

    let workstation_totals = {};
    let raw_workstation_totals = {};
    for (let key in cost_group) {
        let [wt, is_fixedStr] = key.split("_");
        let is_fixed = parseInt(is_fixedStr, 10);

        if (!workstation_totals[wt]) {
            workstation_totals[wt] = { fixed: 0, variable: 0 };
        }
        if (is_fixed) {
            workstation_totals[wt].fixed += cost_group[key];
        } else {
            workstation_totals[wt].variable += cost_group[key];
        }

        // Also track raw costs
        if (!raw_workstation_totals[wt]) {
            raw_workstation_totals[wt] = { fixed: 0, variable: 0 };
        }
        if (is_fixed) {
            raw_workstation_totals[wt].fixed += raw_cost_group[key];
        } else {
            raw_workstation_totals[wt].variable += raw_cost_group[key];
        }
    }

    let final_totals = {};
    let raw_final_totals = {};
    for (let wt in workstation_totals) {
        let fixed_total = workstation_totals[wt].fixed;
        let variable_total = workstation_totals[wt].variable;
        if (wt === "Mass Finishing") {
            final_totals[wt] = variable_total + fixed_total;
        } else {
            final_totals[wt] = ((variable_total * (quantity || 1)) + fixed_total) / (quantity || 1);
        }

        // Calculate raw final totals
        let raw_fixed_total = raw_workstation_totals[wt]?.fixed || 0;
        let raw_variable_total = raw_workstation_totals[wt]?.variable || 0;
        if (wt === "Mass Finishing") {
            raw_final_totals[wt] = raw_variable_total + raw_fixed_total;
        } else {
            raw_final_totals[wt] = ((raw_variable_total * (quantity || 1)) + raw_fixed_total) / (quantity || 1);
        }
    }
    // Calculate total raw cost
    let total_raw_cost = (
        raw_block_material_amount +
        raw_bar_material_amount +
        raw_fixings_cost +
        (raw_final_totals["Machining"] || 0) +
        (raw_final_totals["Finishing"] || 0) +
        (raw_final_totals["Other"] || 0) +
        (raw_final_totals["Inspection"] || 0) +
        (raw_final_totals["Mass Finishing"] || 0) +
        (raw_final_totals["Subcontract"] || 0) +
        (raw_final_totals["Tool Making"] || 0) +
        (raw_final_totals["Turning"] || 0) +
        (raw_final_totals["EDM"] || 0) +
        (raw_final_totals["Design"] || 0) +
        (raw_final_totals["Assembly"] || 0)
    );

    // Get delivery_each from the parent Velocetec Costing Detail if needed in the future
    // Currently not used but keeping the structure for future reference
    if (frm.doc.velocetec_costing_detail) {
        // This section can be used if delivery_each is needed in calculations
    }

    if (frm.doc.velocetec_costing) {
        frappe.call({
            method: "velocetec.api.velocetec_line_costing.update_velocetec_costing_item",
            args: {
                velocetec_costing: frm.doc.velocetec_costing,
                velocetec_costing_detail: frm.doc.velocetec_costing_detail,
                machining: final_totals["Machining"] || 0,
                finishing: final_totals["Finishing"] || 0,
                other: final_totals["Other"] || 0,
                inspection: final_totals["Inspection"] || 0,
                mass_finishing: final_totals["Mass Finishing"] || 0,
                subcontract: final_totals["Subcontract"] || 0,
                tool_making: final_totals["Tool Making"] || 0,
                turning: final_totals["Turning"] || 0,
                edm: final_totals["EDM"] || 0,
                design: final_totals["Design"] || 0,
                material_price: total_material_price,
                fixings: total_fixings_cost,
                sell_cost_each: total_raw_cost,
                ignore_timestamp: true
            },
            callback: function (r) {
                // if (r.message) {
                //     frappe.msgprint(__("Velocetec Costing updated successfully."));
                //     frm.reload_doc();
                // }
            }
        });


    }

    if (!caller) {
        frm.set_value("block_material_amount", block_material_amount);
        frm.set_value("bar_material_amount", bar_material_amount);
        frm.set_value("total_fixings_cost", total_fixings_cost);
    }

    frm.refresh_fields();

    // Reset the calculation in progress flag
    calculationInProgress = false;
}
