// Copyright (c) 2025, Sydney Kibanga and contributors
// For license information, please see license.txt

// Helper function to ensure parent_part field is visible and populated
function ensure_parent_part_field_visible(frm, cdt, cdn) {
    let row = frappe.get_doc(cdt, cdn);
    if (!row || !row.is_child_part) {
        return;
    }

    setTimeout(() => {
        const $gridRow = $(`.grid-row[data-name="${row.name}"]`);
        if ($gridRow.length) {
            const $parentPartField = $gridRow.find('[data-fieldname="parent_part"]');
            if ($parentPartField.length) {
                $parentPartField.closest('.form-group').show();

                const $select = $parentPartField.find('select');
                if ($select.length && $select.find('option').length <= 1) {
                    let potential_parents = [];
                    (frm.doc.items || []).forEach(item => {
                        if (item.name !== row.name && item.part_number) {
                            potential_parents.push(item.part_number);
                        }
                    });
                    potential_parents = [...new Set(potential_parents)];

                    $select.empty();
                    $select.append('<option value=""></option>');
                    potential_parents.forEach(part => {
                        $select.append(`<option value="${part}">${part}</option>`);
                    });

                    if (row.parent_part) {
                        $select.val(row.parent_part);
                    }
                }
            }
        }
    }, 500);
}

// Function to update VLC when quantity changes
function updateVLCOnQuantityChange(frm, cdt, cdn) {
    let row = frappe.get_doc(cdt, cdn);
    if (!row) return;

    row.needs_recalculation = true;

    // Update VLC if it exists for this item
    if (row.velocetec_line_costing) {
        // Call the API to update VLC
        frappe.call({
            method: "velocetec.api.velocetec_costing.update_vlc_on_quantity_change",
            args: {
                vc_detail_id: row.name,
                new_quantity: row.quantity
            },
            callback: function (r) {
                if (r.message && r.message.success) {
                    // Update the VLC data in the row
                    if (r.message.velocetec_line_costing) {
                        frappe.model.set_value(cdt, cdn, "velocetec_line_costing", r.message.velocetec_line_costing);
                    }

                    // Show success message
                    frappe.show_alert({
                        message: __("VLC updated with new quantity"),
                        indicator: "green"
                    }, 3);

                    // Make sure the quantity is updated in the UI
                    frappe.model.set_value(cdt, cdn, "quantity", row.quantity);

                    // Refresh the form to show updated values
                    setTimeout(function () {
                        frm.reload_doc();
                    }, 500);
                } else {
                    // Show error message
                    frappe.show_alert({
                        message: __(r.message.message || "Error updating VLC"),
                        indicator: "red"
                    }, 5);
                }
            }
        });
    }
}

frappe.ui.form.on("Velocetec Costing", {
    setup: function (frm) {
        frm.set_query("quotation_to", function () {
            return {
                filters: {
                    name: ["in", ["Customer", "Lead"]],
                },
            };
        });
    },

    refresh: async function (frm) {
        // Initialize the skip flag if it doesn't exist
        if (frm._skip_recalculation_check === undefined) {
            frm._skip_recalculation_check = false;
        }

        // Initialize _previousQuantity for all items to track quantity changes
        (frm.doc.items || []).forEach(function (child) {
            // Store current quantity as previous quantity for future reference
            if (child._previousQuantity === undefined) {
                child._previousQuantity = child.quantity || 0;
            }
            update_parent_part_options(frm, "Velocetec Costing Detail", child.name);
        });

        // Only run calculation if we're not in the process of unlinking
        // AND if there are items that actually need recalculation
        if (!frm._skip_recalculation_check) {
            // Check if any items actually need recalculation before running
            const needsRecalc = (frm.doc.items || []).some(item => item.needs_recalculation);

            if (needsRecalc) {
                runCostingCalculation(frm);
            }
        }

        if (frm.doc.quotation_link_status === "Linked" && frm.doc.reference_doctype === "Quotation" && frm.doc.reference_name) {
            frm.add_custom_button(__("Unlink Quotation"), function () {
                frappe.confirm(
                    __("Are you sure you want to unlink the Quotation?"),
                    function () {
                        // Use server-side method to unlink the quotation
                        frappe.call({
                            method: "velocetec.api.velocetec_costing.unlink_quotation",
                            args: {
                                vc_docname: frm.doc.name
                            },
                            callback: function (r) {
                                if (r.message && r.message.success) {
                                    // Reload the document to reflect the changes
                                    frm.reload_doc();
                                    frappe.msgprint(__(r.message.message));
                                } else {
                                    frappe.msgprint(__(r.message.message || "Error unlinking quotation"));
                                }
                            }
                        });
                    }
                );
            });
        }

        if (frm.doc.quotation_link_status === "Linked" && frm.doc.reference_doctype === "Quotation" && frm.doc.reference_name) {
            frm.add_custom_button(__("View Quotation"), function () {
                frappe.set_route("Form", "Quotation", frm.doc.reference_name);
            }, __("Actions"));

            // Add Rollback Changes button
            frm.add_custom_button(__("Rollback Changes"), function () {
                frappe.confirm(
                    __("This will discard all unsaved changes. Continue?"),
                    function () {
                        // Simple rollback using frappe.db.rollback()
                        frappe.call({
                            method: "velocetec.velocetec.doctype.velocetec_costing.velocetec_costing.simple_rollback",
                            args: {
                                docname: frm.doc.name
                            },
                            freeze: true,
                            freeze_message: __("Rolling back changes..."),
                            callback: function (r) {
                                frappe.show_alert({
                                    message: __("Changes rolled back successfully."),
                                    indicator: "green"
                                }, 3);
                                frm.reload_doc();
                            }
                        });
                    }
                );
            }, __("Actions"));
        }

        if (frm.doc.reference_opportunity) {
            frm.add_custom_button(__("View Opportunity"), function () {
                frappe.set_route("Form", "Opportunity", frm.doc.reference_opportunity);
            }, __("Actions"));
        }

        // Add Tree Visualization button
        frm.add_custom_button(__("Tree Visualization"), function () {
            show_tree_visualization(frm);
        }, __("Actions"));

        // apply our custom filter
        if (frm.doc.customer) {
            frm.set_query('customer_primary_contact', function () {
                return {
                    query: 'frappe.contacts.doctype.contact.contact.contact_query',
                    filters: {
                        link_doctype: 'Customer',
                        link_name: frm.doc.customer
                    }
                };
            });
        } else {
            frm.set_query('customer_primary_contact', function () {
                return {};
            });

        }

    },

    onload: (frm) => {
        // Initialize the skip flag if it doesn't exist
        if (frm._skip_recalculation_check === undefined) {
            frm._skip_recalculation_check = false;
        }

        // Initialize _previousQuantity for all items to track quantity changes
        (frm.doc.items || []).forEach(function (child) {
            // Store current quantity as previous quantity for future reference
            if (child._previousQuantity === undefined) {
                child._previousQuantity = child.quantity || 0;
            }
            update_parent_part_options(frm, "Velocetec Costing Detail", child.name);
        });

        // Only run calculation if we're not in the process of unlinking
        if (!frm._skip_recalculation_check) {
            runCostingCalculation(frm);
        }
    },

    create_quotation(frm) {
        // Run costing calculation first to ensure all values are up to date
        runCostingCalculation(frm, true).then(() => {
            createQuotation(frm);
        });
    },

    customer: function (frm) {
        // re-apply the filter when customer changes
        frm.set_value('customer_primary_contact', null);
        frm.set_query('customer_primary_contact', function () {
            return {
                query: 'frappe.contacts.doctype.contact.contact.contact_query',
                filters: {
                    link_doctype: 'Customer',
                    link_name: frm.doc.customer
                }
            };
        });
    },

    validate: function (frm) {
        // Validate delivery date hierarchy before saving
        return validateAllDeliveryDates(frm);
    }
});

frappe.ui.form.on("Velocetec Costing Detail", {
    refresh: (frm, cdt, cdn) => {
        update_parent_part_options(frm, cdt, cdn);
    },

    is_child_part: async function (frm, cdt, cdn) {
        let row = frappe.get_doc(cdt, cdn);

        // If unchecking 'Is Child Part', clear the parent_part field
        if (!row.is_child_part) {
            frappe.model.set_value(cdt, cdn, 'parent_part', '');
            frappe.model.set_value(cdt, cdn, 'parent_id', '');
        } else {
            update_parent_part_options(frm, cdt, cdn);

            // Force refresh to ensure the parent_part field is visible
            frm.refresh_field("items");

            setTimeout(() => {
                ensure_parent_part_field_visible(frm, cdt, cdn);
            }, 1000);
        }

        update_parent_part_options(frm, cdt, cdn);

        if (row.is_child_part && row.name && row.name.startsWith("new-")) {
            row = (frm.doc.items || []).find(r => r.idx === row.idx);

            // After reload, ensure the parent_part field is visible
            if (row && row.is_child_part) {
                setTimeout(() => {
                    ensure_parent_part_field_visible(frm, cdt, cdn);
                }, 1000);
            }
        }
    },

    parent_part: async function (frm, cdt, cdn) {
        update_parent_part_options(frm, cdt, cdn);

        let row = frappe.get_doc(cdt, cdn);
        if (!row.parent_part) {
            frappe.model.set_value(cdt, cdn, "parent_id", "");
            return;
        }

        // Find the row with the matching part_number
        let parentRow = (frm.doc.items || []).find(r => r.part_number === row.parent_part);
        if (!parentRow) {
            frappe.model.set_value(cdt, cdn, "parent_id", "");
            return;
        }

        // Check if this would create bidirectional linking (parent becoming child of its own descendant)
        if (is_descendant_of_simple(frm, row.name, parentRow.name)) {
            frappe.msgprint({
                title: __('Invalid Parent Selection'),
                indicator: 'red',
                message: __('Cannot select "{0}" as parent because it is a descendant of "{1}". This would create bidirectional linking.', [parentRow.part_number, row.part_number])
            });
            frappe.model.set_value(cdt, cdn, "parent_part", "");
            frappe.model.set_value(cdt, cdn, "parent_id", "");
            return;
        }

        // Simple validation: Check if this would create a circular reference
        if (would_create_circular_reference_simple(frm, row, parentRow)) {
            frappe.msgprint({
                title: __('Circular Reference'),
                indicator: 'red',
                message: __('Cannot link "{0}" to "{1}" as it would create a circular reference.', [row.part_number, parentRow.part_number])
            });
            frappe.model.set_value(cdt, cdn, "parent_part", "");
            frappe.model.set_value(cdt, cdn, "parent_id", "");
            return;
        }

        // If the parent row is unsaved, save the form first
        if (parentRow.name && parentRow.name.startsWith("new-")) {
            if (frm.doc.__unsaved) {
                await frm.save();
                // Reload to ensure we have the latest data
                await frm.reload_doc();
                // Get the updated parent row after reload
                parentRow = (frm.doc.items || []).find(r => r.part_number === row.parent_part);
                if (!parentRow) {
                    frappe.model.set_value(cdt, cdn, "parent_id", "");
                    return;
                }
            }
        }

        frappe.model.set_value(cdt, cdn, "parent_id", parentRow.name);

        // Ensure the parent_id is properly set by checking after a short delay
        setTimeout(() => {
            row = frappe.get_doc(cdt, cdn);
            if (!row.parent_id && row.parent_part) {
                let parentRow = (frm.doc.items || []).find(r => r.part_number === row.parent_part);
                if (parentRow) {
                    frappe.model.set_value(cdt, cdn, "parent_id", parentRow.name);
                }
            }
        }, 500);

        frm.refresh_fields();
    },

    part_number: async function (frm, cdt, cdn) {
        let row = frappe.get_doc(cdt, cdn);
        if (!row.part_number) {
            return;
        }
        update_parent_part_options(frm, cdt, cdn);

        if (!row.is_child_part) {
            // Update child parts with the new parent part number
            update_child_part_details(frm, row);

            // Update duplicated items to use the new part number pattern
            update_duplicated_items(frm, row);
        }

        // Update VLC part number if it exists
        update_vlc_part_number(row);

        // Replace get_value with get_list
        let item_result = await frappe.call({
            method: "frappe.client.get_list",
            args: {
                doctype: "Item",
                filters: {
                    item_code: row.part_number
                },
                fields: ["name"],
                limit_page_length: 1,
                ignore_permissions: true
            }
        });

        // If the part exists, ask user if they want to open it
        if (item_result.message && item_result.message.length > 0) {
            frappe.confirm(
                __("Part {0} already exists. Do you want to open its details?", [row.part_number]),
                function () {
                    let url = frappe.urllib.get_base_url() + "/app/item/" + row.part_number;
                    window.open(url, "_blank");
                },
                function () { }
            );
        }
    },

    routing_details: function (frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        let old_idx = row.idx;

        function process_routing() {
            frm.reload_doc().then(() => {
                let updated_row = (frm.doc.items || []).find(i => i.idx === old_idx);
                if (!updated_row) {
                    frappe.throw("Could not find the updated row after reload. Aborting.");
                    return;
                }
                if (!updated_row.part_number) {
                    frappe.msgprint(__("Part number is empty. Cannot route."));
                    return;
                }
                if (updated_row.part_number.indexOf("*") !== -1 || updated_row.part_number.toLowerCase().includes("copy")) {
                    frappe.msgprint(__("Routing not performed for duplicate or temporary part numbers."));
                    return;
                }

                // First check if a VLC already exists for this item
                frappe.call({
                    method: "frappe.client.get_list",
                    args: {
                        doctype: "Velocetec Line Costing",
                        fields: ["name"],
                        filters: {
                            velocetec_costing: frm.doc.name,
                            velocetec_costing_detail: updated_row.name
                        },
                        limit_page_length: 1
                    },
                    callback: function (r) {
                        if (r.message && r.message.length > 0) {
                            // VLC already exists, navigate to it
                            frappe.set_route("Form", "Velocetec Line Costing", r.message[0].name);
                        } else if (
                            updated_row.dup_reference_part_number &&
                            updated_row.dup_reference_part_number_name &&
                            !updated_row.is_dup_reference_created
                        ) {
                            // This is a duplicated item without a VLC, create one from the original
                            frappe.call({
                                method: "frappe.client.get_list",
                                args: {
                                    doctype: "Velocetec Line Costing",
                                    filters: {
                                        velocetec_costing_detail: updated_row.dup_reference_part_number_name
                                    },
                                    fields: ["name"],
                                    limit_page_length: 1
                                }
                            }).then(r => {
                                if (r && r.message && r.message.length > 0) {
                                    let old_vlc_name = r.message[0].name;
                                    frappe.db.get_doc("Velocetec Line Costing", old_vlc_name).then(old_vlc => {
                                        let data = JSON.parse(JSON.stringify(old_vlc));

                                        // Remove fields that might cause issues during duplication
                                        ["name", "creation", "modified", "modified_by", "owner", "docstatus", "idx"].forEach(field => {
                                            if (data[field]) delete data[field];
                                        });

                                        // Update with the current item's data
                                        data["part_number"] = updated_row.part_number;
                                        data["description"] = updated_row.description;
                                        data["velocetec_costing_detail"] = updated_row.name;
                                        data["velocetec_costing"] = frm.doc.name;

                                        // Create and save the new VLC
                                        let new_vlc = frappe.new_doc("Velocetec Line Costing");
                                        new_vlc.update(data);
                                        new_vlc.save().then(doc => {
                                            // Mark as created to prevent duplicate creation
                                            frappe.model.set_value(updated_row.doctype, updated_row.name, "is_dup_reference_created", 1);

                                            // Navigate to the new VLC
                                            frappe.set_route("Form", "Velocetec Line Costing", doc.name);
                                        }).catch(e => {
                                            frappe.msgprint(__("Error creating VLC: " + e));
                                        });
                                    });
                                } else {
                                    // No original VLC found, create a new one
                                    createNewVLC(updated_row);
                                }
                            });
                        } else {
                            // Regular item without a VLC, create a new one
                            createNewVLC(updated_row);
                        }
                    }
                });
            });
        }

        function createNewVLC(updated_row) {
            let routingDoc = {
                doctype: "Velocetec Line Costing",
                velocetec_costing: frm.doc.name,
                part_number: updated_row.part_number,
                description: updated_row.description,
                velocetec_costing_detail: updated_row.name
            };

            frappe.call({
                method: "frappe.client.insert",
                args: {
                    doc: routingDoc
                },
                callback: function (r) {
                    if (!r.exc) {
                        frappe.set_route("Form", "Velocetec Line Costing", r.message.name);
                    }
                }
            });
        }

        if (frm.doc.__unsaved) {
            frm.save().then(() => {
                process_routing();
            });
        } else {
            process_routing();
        }
    },

    duplicate: async function (frm, cdt, cdn) {
        if (frm.doc.docstatus === 1) {
            frappe.show_alert({
                message: __("This Costing Calculation is submitted; duplication is disabled."),
                indicator: "blue"
            }, 3);
            return;
        }
        let row = frappe.get_doc(cdt, cdn);
        frappe.call({
            method: "velocetec.api.velocetec_costing.duplicate_costing_tree_node",
            args: {
                detail_name: row.name,
                duplicate_from_child: row.is_child_part
            },
            callback: function (r) {
                if (r.message) {
                    frappe.show_alert({
                        message: __("Duplication successful. New node created."),
                        indicator: "blue"
                    }, 3);
                    frm.reload_doc();
                }
            }
        });
    },

    before_items_remove: function (frm, cdt, cdn) {
        let removed_row = frappe.get_doc(cdt, cdn);
        frappe.call({
            method: "frappe.client.get_list",
            args: {
                doctype: "Velocetec Line Costing",
                fields: ["name"],
                filters: {
                    velocetec_costing: frm.doc.name,
                    velocetec_costing_detail: removed_row.name
                },
                limit_page_length: 1
            },
            callback: function (r) {
                if (r.message && r.message.length > 0) {
                    frappe.call({
                        method: "frappe.client.delete",
                        args: {
                            doctype: "Velocetec Line Costing",
                            name: r.message[0].name
                        },
                        callback: function () {
                            frappe.show_alert({
                                message: __("Linked VLC record deleted successfully."),
                                indicator: "red"
                            }, 3);
                        }
                    });
                }
            }
        });
    },

    quantity: function (frm, cdt, cdn) {
        // Get the current row and save its quantity value
        let row = frappe.get_doc(cdt, cdn);
        if (!row) return;

        let currentQuantity = row.quantity || 0;

        // Check if this is a root node (not a child part)
        if (!row.is_child_part) {
            // Function to get all descendants (direct and indirect children) of a node
            function getAllDescendants(parentId, parentPartNumber) {
                let descendants = [];

                // Find direct children
                const directChildren = frm.doc.items.filter(child =>
                    child.is_child_part &&
                    ((child.parent_id && child.parent_id === parentId) ||
                        (!child.parent_id && child.parent_part === parentPartNumber))
                );

                // Add direct children to descendants
                descendants = descendants.concat(directChildren);

                // Recursively add children of children
                directChildren.forEach(child => {
                    const childDescendants = getAllDescendants(child.name, child.part_number);
                    descendants = descendants.concat(childDescendants);
                });

                return descendants;
            }

            // Get all descendants of the current node
            const allDescendants = getAllDescendants(row.name, row.part_number);

            // If it has descendants, show a message
            if (allDescendants && allDescendants.length > 0) {
                // Create a list of child parts with their current quantities
                const childPartsList = allDescendants.map(child => {
                    const qty = child.quantity || 0;
                    return `<li>${child.part_number || ""} - Qty: ${qty}</li>`;
                }).join('');

                // Just show a message about which child items should be updated
                frappe.msgprint({
                    title: __("Child Parts to Update"),
                    message: `<p>You've changed the quantity of a parent part to ${currentQuantity}.</p>
                    <p>Remember to update the following child parts Current Quantities:</p>
                    <ul>${childPartsList}</ul>`,
                    indicator: "blue"
                });
            }
        }

        // First, explicitly set the quantity in the database to ensure it's saved
        frappe.db.set_value(cdt, cdn, 'quantity', currentQuantity)
            .then(function () {
                // Then update the VLC with the new quantity
                updateVLCOnQuantityChange(frm, cdt, cdn);
            });
    },

    mark_up_item: function (frm) {
        runCostingCalculation(frm);
    },

    delivery_cost: function (frm) {
        // Mark all items as needing recalculation when delivery cost changes
        (frm.doc.items || []).forEach(function (item) {
            item.needs_recalculation = true;
        });
        runCostingCalculation(frm);
    },

    no_of_shipments: function (frm) {
        // Mark all items as needing recalculation when no_of_shipments changes
        (frm.doc.items || []).forEach(function (item) {
            item.needs_recalculation = true;
        });
        runCostingCalculation(frm);
    },

    delivery_date: function (frm, cdt, cdn) {
        // Validate delivery date hierarchy when delivery_date changes
        validateDeliveryDateHierarchy(frm, cdt, cdn);
    },

    add_to_parts: function (frm) {
        // Handle delivery fields immediately when add_to_parts changes
        if (frm.doc.add_to_parts) {
            // When Add to Parts is checked, populate delivery fields from document level to items
            (frm.doc.items || []).forEach(function (item) {
                item.delivery_type = frm.doc.delivery_type || "";
                item.delivery_cost = frm.doc.delivery_cost || "";
                item.no_of_shipments = frm.doc.no_of_shipments || "";
                item.delivery_total = frm.doc.delivery_total || "";
            });
        } else {
            // When Add to Parts is unchecked, clear all delivery fields from items
            (frm.doc.items || []).forEach(function (item) {
                item.delivery_each = 0;
                item.delivery_type = "";
                item.delivery_cost = "";
                item.no_of_shipments = "";
                item.delivery_total = "";
            });
        }

        // Refresh the items grid to show the changes immediately
        frm.refresh_field("items");
    },

    items_add: function (frm, cdt, cdn) {
        let row = frappe.get_doc(cdt, cdn);

        // Initialize the new row
        if (row && row.is_child_part) {
            // Make sure parent_part field is visible and populated
            setTimeout(() => {
                update_parent_part_options(frm, cdt, cdn);
                frm.refresh_field("items");
                ensure_parent_part_field_visible(frm, cdt, cdn);
            }, 300);
        }

        runCostingCalculation(frm);
        update_parent_part_options(frm, cdt, cdn);
    },

    items_remove: function (frm, cdt, cdn) {
        update_parent_part_options(frm, cdt, cdn);
        runCostingCalculation(frm);
    },

    form_render: function (frm, cdt, cdn) {
        let row = frappe.get_doc(cdt, cdn);

        // Check if this is a child part
        if (row && row.is_child_part) {
            // Make sure parent_part field is visible and populated
            setTimeout(() => {
                update_parent_part_options(frm, cdt, cdn);
                frm.refresh_field("items");
                ensure_parent_part_field_visible(frm, cdt, cdn);
            }, 300);
        }

        update_parent_part_options(frm, cdt, cdn);
    }
});

async function update_child_part_details(frm, row) {
    let children = (frm.doc.items || []).filter(r =>
        (r.parent_id === row.name) ||
        (r.is_child_part && !r.parent_part) ||
        (!r.parent_id && r.parent_part && r.parent_part === row.part_number)
    );
    if (!children || children.length === 0) {
        return;
    }
    children.forEach(d => {
        frappe.model.set_value(d.doctype, d.name, "parent_part", row.part_number || "");
    });
    frm.refresh_field("items");
}

async function update_duplicated_items(frm, row) {
    // Find all items that have this row's name as their dup_reference_part_number_name
    // These are items that were duplicated from this row
    let duplicated_items = (frm.doc.items || []).filter(r =>
        r.dup_reference_part_number_name &&
        r.part_number &&
        r.part_number.startsWith("*copy*") &&
        r.dup_reference_part_number_name === row.name
    );

    if (!duplicated_items || duplicated_items.length === 0) {
        return;
    }

    // Update the duplicated items
    duplicated_items.forEach(d => {
        // Update the dup_reference_part_number to match the new parent part number
        frappe.model.set_value(d.doctype, d.name, "dup_reference_part_number", row.part_number);

        // Update the part number to match the new pattern
        let new_part_number = "*copy*" + (row.part_number || "");
        frappe.model.set_value(d.doctype, d.name, "part_number", new_part_number);

        // Also update any children of this duplicated item
        let dup_children = (frm.doc.items || []).filter(c =>
            c.parent_id === d.name ||
            (!c.parent_id && c.parent_part && c.parent_part === d.part_number)
        );

        dup_children.forEach(c => {
            frappe.model.set_value(c.doctype, c.name, "parent_part", new_part_number);
        });
    });

    frm.refresh_field("items");
}

async function update_vlc_part_number(row) {
    // Check if this row has a VLC
    if (!row.velocetec_line_costing) {
        return;
    }

    try {
        // Parse the VLC data
        let vlc_data;
        try {
            vlc_data = JSON.parse(row.velocetec_line_costing);
        } catch (e) {
            console.error("Error parsing VLC data:", e);
            return;
        }

        if (!vlc_data || !vlc_data.name) {
            return;
        }

        // Get the VLC document
        const vlc_doc = await frappe.db.get_doc("Velocetec Line Costing", vlc_data.name);

        // Update the part number
        if (vlc_doc && vlc_doc.part_number !== row.part_number) {
            // Save the VLC document with the updated part number
            await frappe.db.set_value("Velocetec Line Costing", vlc_doc.name, {
                part_number: row.part_number
            });
        }
    } catch (e) {
        console.error("Error updating VLC part number:", e);
    }
}

// Function to check if a part has raw materials in its VLC
async function checkPartHasRawMaterials(item) {
    try {
        // Call the server-side method to check for raw materials
        const result = await frappe.call({
            method: "velocetec.velocetec.doctype.velocetec_costing.velocetec_costing.check_part_has_raw_materials",
            args: {
                item_name: item.name,
                has_children: item._has_children ? 1 : 0
            }
        });

        // Return the result from the server
        return result.message;
    } catch (e) {
        console.error("Error checking for raw materials:", e);
        return false;
    }
}

async function createQuotation(frm) {
    // First, save the document to ensure all calculations are up to date
    if (frm.doc.__unsaved) {
        await frm.save();
        // Reload to ensure we have the latest data
        await frm.reload_doc();
    }

    if (frm.doc.reference_doctype === "Quotation" && frm.doc.reference_name) {
        frappe.set_route("Form", "Quotation", frm.doc.reference_name);
        return;
    }

    if (frm.doc.reference_doctype === "Quotation" && frm.doc.reference_name) {
        let q_status_response = await frappe.call({
            method: "frappe.client.get_list",
            args: {
                doctype: "Quotation",
                filters: { name: frm.doc.reference_name },
                fields: ["docstatus"],
                limit_page_length: 1
            }
        });
        if (q_status_response && q_status_response.message && q_status_response.message.length > 0) {
            let q_status = q_status_response.message[0].docstatus;
            if (q_status === 0) {
                await askChildPartsAndUpdateQuotation(frm);
                return;
            } else {
                frappe.set_route("Form", "Quotation", frm.doc.reference_name);
                return;
            }
        }
    } else {
        // Check for parts without raw materials before creating quotation

        // First, mark items that have children
        frm.doc.items.forEach(item => {
            item._has_children = frm.doc.items.some(child =>
                child.is_child_part &&
                ((child.parent_id && child.parent_id === item.name) ||
                    (!child.parent_id && child.parent_part === item.part_number))
            );
        });

        // Check each item for raw materials
        const itemsWithoutRawMaterials = [];
        for (const item of frm.doc.items) {
            const hasRawMaterials = await checkPartHasRawMaterials(item);
            if (!hasRawMaterials) {
                itemsWithoutRawMaterials.push(item);
            }
        }

        // If there are items without raw materials, block quotation creation
        if (itemsWithoutRawMaterials.length > 0) {
            const partsList = itemsWithoutRawMaterials.map(item =>
                `<li>Row #${item.idx} - <strong>${item.part_number}</strong>${item.description ? ' - ' + item.description : ''}</li>`
            ).join('');

            frappe.msgprint({
                title: __('Quotation Creation Blocked'),
                indicator: 'red',
                message: `<p><strong>Cannot create quotation:</strong> The following parts do not have any raw materials (block, bar, or fixings) in their VLC:</p>
                <ul style="color: red; margin-left: 20px;">${partsList}</ul>
                <p><strong>Action required:</strong> Please add block, bar, or Additional materials to these parts in their VLC before creating a quotation. BOMs cannot be created without raw materials.</p>`
            });

            // Show alert for immediate feedback
            frappe.show_alert({
                message: __("Quotation creation blocked. Please add raw materials (block, bar, or Additional) to the highlighted parts."),
                indicator: "red"
            }, 8);

            return; // Stop execution - do not proceed with quotation creation
        } else {
            // All parts have raw materials, proceed normally
            await askChildPartsAndUpdateQuotation(frm);
        }
    }
}

async function askChildPartsAndUpdateQuotation(frm) {
    let child_parts = (frm.doc.items || []).filter(row => row.is_child_part);
    let parent_parts = (frm.doc.items || []).filter(row => !row.is_child_part);

    let toggle_html = `
      <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
        <h6 style="margin-bottom: 10px; color: #333;">Quotation Display Mode (Select One):</h6>
        <div style="margin-bottom: 8px;">
          <label><input type="radio" name="display-mode" id="toggle-parent" value="parent" checked> <strong>Top Level Quote Mode</strong></label>
          <small style="display: block; margin-left: 20px; color: #666;">
            Show parent parts with full cost (includes child costs) + child part numbers in description
          </small>
        </div>
        <div style="margin-bottom: 8px;">
          <label><input type="radio" name="display-mode" id="toggle-child" value="child"> <strong>Breakdown Quote Mode</strong></label>
          <small style="display: block; margin-left: 20px; color: #666;">
            Show ALL parts as individual line items with their own selling costs + delivery distribution
          </small>
        </div>
        <div class="toggle-warning" style="margin-top: 10px; padding: 8px; background-color: #fff3cd; border-radius: 3px;">
          <small style="color: #856404;">
            <strong>Important:</strong>
            <ul style="margin: 5px 0; padding-left: 15px;">
              <li><strong>Top Level Quote Mode:</strong> No double-counting - parent cost includes all child costs</li>
              <li><strong>Breakdown Quote Mode:</strong> Shows ALL parts with individual selling costs - includes delivery distribution to leaf nodes</li>
            </ul>
          </small>
        </div>
      </div>
    `;

    let parent_html = `<h5>Parent Parts</h5> <table class="table table-bordered">
        <thead>
          <tr>
            <th>Part Number</th>
            <th>Description</th>
            <th>Quantity</th>
            <th>Include</th>
          </tr>
        </thead>
        <tbody>`;
    parent_parts.forEach(row => {
        parent_html += `
          <tr>
            <td>${row.part_number || ""}</td>
            <td>${row.description || ""}</td>
            <td>${row.quantity || 0}</td>
            <td><input type="checkbox" class="parent-checkbox" data-row="${row.part_number}" checked></td>
          </tr>`;
    });
    parent_html += `</tbody></table>`;

    let child_html = `<h5>Child Parts</h5> <table class="table table-bordered">
        <thead>
          <tr>
            <th>Part Number</th>
            <th>Description</th>
            <th>Quantity</th>
            <th>Include</th>
          </tr>
        </thead>
        <tbody>`;
    child_parts.forEach(row => {
        child_html += `
          <tr>
            <td>${row.part_number || ""}</td>
            <td>${row.description || ""}</td>
            <td>${row.quantity || 0}</td>
            <td><input type="checkbox" class="child-checkbox" data-row="${row.part_number}"></td>
          </tr>`;
    });
    child_html += `</tbody></table>`;

    let html = toggle_html + parent_html + child_html;

    let d = new frappe.ui.Dialog({
        title: __("Select Parts to Include in Quotation"),
        fields: [{
            fieldname: "parts_table",
            fieldtype: "HTML",
            options: html
        }],
        primary_action_label: __("Create/Update Quotation"),
        primary_action: async function () {
            d.hide();
            let selected_parent_numbers = [];
            $(d.wrapper).find(".parent-checkbox:checked").each(function () {
                selected_parent_numbers.push($(this).attr("data-row"));
            });
            let selected_child_numbers = [];
            $(d.wrapper).find(".child-checkbox:checked").each(function () {
                selected_child_numbers.push($(this).attr("data-row"));
            });

            // Make sure the document is saved before creating the quotation
            if (frm.doc.__unsaved) {
                await frm.save();
                // Reload to ensure we have the latest data
                await frm.reload_doc();
            }

            let vc_name = frm.doc.custom_vc_name || "";
            let vc_docname = frm.doc.name;

            await frappe.call({
                method: "velocetec.api.velocetec_costing.create_quotation_from_selection",
                args: {
                    vc_name: vc_name,
                    vc_docname: vc_docname,
                    selected_parents: JSON.stringify(selected_parent_numbers),
                    selected_children: JSON.stringify(selected_child_numbers)
                },
                callback: function (r) {
                    if (r.message) {
                        frappe.msgprint(__("Quotation created successfully: {0}", [r.message.quotation]));
                        if (r.message.reload_doc) {
                            frm.reload_doc();
                            frappe.set_route("Form", "Quotation", r.message.quotation);
                        }
                    }
                }
            });
        }
    });
    d.show();

    // Function to update the warning message and checkboxes based on selected mode
    function updateToggleWarning() {
        const selectedMode = $(d.wrapper).find('input[name="display-mode"]:checked').val();
        const warningDiv = $(d.wrapper).find('.toggle-warning');

        if (selectedMode === 'parent') {
            warningDiv.html('<small style="color: #0c5460;"><strong>Top Level Quote Mode:</strong> Parent parts with full cost (includes child costs) + child part numbers listed (no child descriptions/costs).</small>');
            // In Top Level Quote Mode, check all parent checkboxes and uncheck all child checkboxes
            $(d.wrapper).find('.parent-checkbox').prop('checked', true);
            $(d.wrapper).find('.child-checkbox').prop('checked', false);
        } else if (selectedMode === 'child') {
            warningDiv.html('<small style="color: #0c5460;"><strong>Breakdown Quote Mode:</strong> ALL parts shown with individual selling costs + delivery distribution applied to leaf nodes.</small>');
            // In Breakdown Quote Mode, check ALL checkboxes (both parent and child) since we want to include all parts
            $(d.wrapper).find('.child-checkbox').prop('checked', true);
            $(d.wrapper).find('.parent-checkbox').prop('checked', true);
        }
        warningDiv.show();
    }

    // Handle radio button changes
    $(d.wrapper).on('change', 'input[name="display-mode"]', function () {
        updateToggleWarning();
    });

    // Initialize warning message
    updateToggleWarning();
}

function update_parent_part_options(frm, cdt, cdn) {
    const current_row = frappe.get_doc(cdt, cdn);
    if (!current_row) {
        return;
    }

    function getDescendants(parentName, items) {
        let descendants = [];
        items.forEach(row => {
            if (row.parent_id === parentName) {
                descendants.push(row.name);
                descendants = descendants.concat(getDescendants(row.name, items));
            }
        });
        return descendants;
    }

    // Find the tree root for current row (to prevent cross-tree linking)
    function findTreeRoot(itemName, items) {
        let item = items.find(i => i.name === itemName);
        if (!item) return null;

        // If item is not a child, it's a root
        if (!item.is_child_part) {
            return item.name;
        }

        // Follow parent chain to find root
        let visited = new Set();
        let current = item;

        while (current && current.is_child_part && !visited.has(current.name)) {
            visited.add(current.name);

            let parent_id = current.parent_id;
            if (!parent_id && current.parent_part) {
                // Find parent by part_number
                let parent = items.find(p => p.part_number === current.parent_part);
                parent_id = parent ? parent.name : null;
            }

            if (!parent_id) break;

            current = items.find(i => i.name === parent_id);
        }

        return current ? current.name : null;
    }

    let items = frm.doc.items || [];
    const descendants = getDescendants(current_row.name, items);
    const current_tree_root = findTreeRoot(current_row.name, items);

    let potential_parents = [];
    items.forEach(row => {
        if (
            row.name !== current_row.name &&
            !descendants.includes(row.name) &&
            row.part_number
        ) {
            // Check if this potential parent is in a different tree (cross-tree linking prevention)
            const potential_parent_root = findTreeRoot(row.name, items);

            // Allow linking if:
            // 1. Current item has no tree root (new item)
            // 2. Potential parent has no tree root (new parent)
            // 3. Both are in the same tree
            // 4. Current item is new and can join any tree
            if (!current_tree_root || !potential_parent_root || current_tree_root === potential_parent_root) {
                potential_parents.push(row.part_number);
            }
        }
    });
    potential_parents = [...new Set(potential_parents)];

    // Safely update grid options
    if (frm.fields_dict["items"] && frm.fields_dict["items"].grid) {
        const grid = frm.fields_dict["items"].grid;

        // Reset visible columns to force refresh
        grid.visible_columns = undefined;
        grid.setup_visible_columns();

        // Check if fields_map and parent_part exist before setting options
        if (grid.fields_map && grid.fields_map.parent_part) {
            grid.fields_map.parent_part.options = potential_parents;
        }

        // Force grid refresh
        grid.refresh();
    }

    // Update options for the current row specifically
    if (current_row.is_child_part) {
        // Find the grid row for the current row
        let grid_row = null;
        if (frm.fields_dict["items"] && frm.fields_dict["items"].grid && frm.fields_dict["items"].grid.grid_rows) {
            grid_row = frm.fields_dict["items"].grid.grid_rows.find(r => r.doc && r.doc.name === current_row.name);
        }

        if (grid_row) {
            // Update the parent_part field options for this specific row
            if (grid_row.docfields) {
                const parent_part_field = grid_row.docfields.find(f => f.fieldname === 'parent_part');
                if (parent_part_field) {
                    parent_part_field.options = potential_parents;

                    // Force the field to refresh
                    if (grid_row.refresh_field) {
                        grid_row.refresh_field('parent_part');
                    }
                }
            }
        }
    }

    // Safely update options for each row's parent_part field
    if (frm.fields_dict["items"] && frm.fields_dict["items"].grid && frm.fields_dict["items"].grid.grid_rows) {
        frm.fields_dict["items"].grid.grid_rows.forEach(row => {
            if (row && row.docfields) {
                row.docfields.forEach(docfield => {
                    if (docfield && docfield.fieldname === 'parent_part') {
                        docfield.options = potential_parents;
                    }
                });
            }
        });
    }

    // Refresh the form field
    frm.refresh_field("items");

    // Only refresh grid if it exists
    if (frm.fields_dict["items"] && frm.fields_dict["items"].grid) {
        const grid = frm.fields_dict["items"].grid;
        grid.refresh();
        grid.setup_visible_columns();
    }
}

async function runCostingCalculation(frm, force_full_recalculation = false) {
    // Skip the check if we're in the process of unlinking
    if (frm.doc.quotation_link_status == "Linked" && !frm._skip_recalculation_check) {
        frappe.show_alert({
            message: __("This VC is Linked; recalculation is disabled."),
            indicator: "blue"
        });
        return;
    }

    // Set flag for full recalculation if forced
    frm.full_recalculation = force_full_recalculation;

    // STEP 1: Update item data from VLC if needed
    for (let item of (frm.doc.items || [])) {
        // Only recalculate if the item needs recalculation (quantity changed) or if it's a full recalculation
        if (item.velocetec_line_costing && (item.needs_recalculation || frm.full_recalculation)) {
            let vlc_temp;
            try {
                vlc_temp = JSON.parse(item.velocetec_line_costing);
            } catch (e) {
                console.error("Error parsing velocetec_line_costing:", e);
                continue;
            }

            try {
                const response = await frappe.call({
                    method: "velocetec.api.velocetec_costing.re_calculate_vlc_logic",
                    args: {
                        docname: vlc_temp.name,
                        qty: item.quantity,
                    }
                });

                if (response && response.message && response.message.velocetec_line_costing) {
                    const velocetec_line_costing = response.message.velocetec_line_costing;
                    if (velocetec_line_costing) {
                        const vlc = JSON.parse(velocetec_line_costing);
                        // Reset the recalculation flag after successful recalculation
                        item.needs_recalculation = false;

                        // Update item fields based on VLC data
                        item.machining = vlc.machining || 0;
                        item.finishing = vlc.finishing || 0;
                        item.other = vlc.other || 0;
                        item.inspection = vlc.inspection || 0;
                        item.mass_finishing = vlc.mass_finishing || 0;
                        item.sub_con = vlc.sub_con || 0;
                        item.tool_making = vlc.tool_making || 0;
                        item.turning = vlc.turning || 0;
                        item.design = vlc.design || 0;
                        item.edm = vlc.edm || 0;
                        item.assembly = vlc.assembly || 0;
                        item.material_price = vlc.material_price || 0;
                        item.fixings = vlc.total_fixings_cost || 0;
                    }
                }
            } catch (err) {
                console.error("Error in re_calculate_vlc_logic:", err);
                frappe.msgprint("Failed to recalculate costs. Please try again.");
            }
        } else if (item.velocetec_line_costing) {
            // If no recalculation needed, just parse the existing data
            try {
                const vlc = JSON.parse(item.velocetec_line_costing);

                // Update item fields based on VLC data
                item.machining = vlc.machining || 0;
                item.finishing = vlc.finishing || 0;
                item.other = vlc.other || 0;
                item.inspection = vlc.inspection || 0;
                item.mass_finishing = vlc.mass_finishing || 0;
                item.sub_con = vlc.sub_con || 0;
                item.tool_making = vlc.tool_making || 0;
                item.turning = vlc.turning || 0;
                item.design = vlc.design || 0;
                item.edm = vlc.edm || 0;
                item.assembly = vlc.assembly || 0;
                item.material_price = vlc.material_price || 0;
                item.fixings = vlc.total_fixings_cost || 0;
            } catch (e) {
                console.error("Error parsing existing velocetec_line_costing:", e);
            }
        }

        // Update parent_id if needed
        if (item.parent && item.parent_part) {
            try {
                let r = await frappe.call({
                    method: "velocetec.api.velocetec_costing.get_parent_id",
                    args: {
                        parent: item.parent,
                        parent_part_number: item.parent_part
                    }
                });
                item.parent_id = r.message || "";
            } catch (e) {
                console.error("Could not fetch parent_id:", e);
            }
        }
    }

    // Enhanced helper function to safely convert any value to a number
    // This function is critical for preventing string concatenation bugs during duplication
    function safeNumber(value) {
        // Handle null, undefined, empty string
        if (value === null || value === undefined || value === "") return 0;

        // If already a number, return it
        if (typeof value === 'number') return isNaN(value) ? 0 : value;

        // If it's a string, try to parse it
        if (typeof value === 'string') {
            // Remove any non-numeric characters except decimal point and minus sign
            const cleanValue = value.replace(/[^\d.-]/g, '');
            const num = parseFloat(cleanValue);
            return isNaN(num) ? 0 : num;
        }

        // For any other type, try to convert to number
        const num = Number(value);
        return isNaN(num) ? 0 : num;
    }

    // STEP 2: Calculate base costs for each item (without children's costs)
    frm.doc.items.forEach(item => {
        // Calculate the base cost for this item (its own cost without children)
        const base_cost = (
            safeNumber(item.material_price) +
            safeNumber(item.machining) +
            safeNumber(item.design) +
            safeNumber(item.finishing) +
            safeNumber(item.inspection) +
            safeNumber(item.other) +
            safeNumber(item.mass_finishing) +
            safeNumber(item.sub_con) +
            safeNumber(item.fixings) +
            safeNumber(item.tool_making) +
            safeNumber(item.turning) +
            safeNumber(item.edm) +
            safeNumber(item.assembly) +
            safeNumber(item.delivery_each)
        );

        // Store the base cost in a temporary property - ensure it's a number and always rounded up
        item._base_cost = Math.ceil(safeNumber(base_cost));

        // Initialize total cost (will be updated in the next step) - ensure it's a number
        item._total_cost = Math.ceil(safeNumber(base_cost));

        // Mark all items as not processed yet
        item._processed = false;
    });

    // STEP 3: Build a map of parent-child relationships for faster lookup
    const childrenMap = {};
    frm.doc.items.forEach(item => {
        if (item.is_child_part) {
            // Determine the parent ID
            const parentId = item.parent_id ||
                (item.parent_part ? frm.doc.items.find(p => p.part_number === item.parent_part)?.name : null);

            if (parentId) {
                if (!childrenMap[parentId]) {
                    childrenMap[parentId] = [];
                }
                childrenMap[parentId].push(item.name);
            }
        }
    });

    // Helper function to get child contribution (child cost * quantity)
    function getChildContribution(child) {
        // Calculate child's contribution to parent cost: child.sell_cost_each * child.quantity with ceiling rounding
        return Math.ceil(safeNumber(child.sell_cost_each) * safeNumber(child.quantity));
    }

    // Helper function to check if an item is a leaf node (has no children)
    // ANY item (whether child or independent) that has no children should get delivery cost
    function isLeafNodeOrIndependent(item) {
        // Check if this item has any children (regardless of whether it's a child or independent part)
        const hasChildren = frm.doc.items.some(child =>
            child.is_child_part &&
            ((child.parent_id && child.parent_id === item.name) ||
                (!child.parent_id && child.parent_part === item.part_number))
        );
        // Return true if item has no children (is a leaf node)
        return !hasChildren;
    }

    // STEP 4: Calculate total costs recursively through the hierarchy
    function calculateTotalCost(itemName) {
        const item = frm.doc.items.find(i => i.name === itemName);
        if (!item || item._processed) {
            // Ensure we always return a number, never undefined or string
            return safeNumber(item?._total_cost || 0);
        }

        // Mark as processed to avoid infinite recursion
        item._processed = true;

        // Start with the item's own base cost - ensure it's a number
        let totalCost = Math.ceil(safeNumber(item._base_cost));

        // Add costs of all children recursively, accounting for their quantities
        const children = childrenMap[itemName] || [];
        children.forEach(childName => {
            const childItem = frm.doc.items.find(i => i.name === childName);
            if (childItem) {
                // First ensure the child's cost is calculated
                const childBaseCost = safeNumber(calculateTotalCost(childName));
                // Then add the child's contribution (base cost * quantity) with ceiling rounding
                const childContribution = Math.ceil(childBaseCost * safeNumber(childItem.quantity));
                totalCost = safeNumber(totalCost) + safeNumber(childContribution);
            }
        });

        // Store the total cost - ensure it's a number
        item._total_cost = safeNumber(totalCost);

        // Update the item's cost fields - ensure they're numbers
        item.each_cost = safeNumber(totalCost);

        // For child items, sell_price_each should only include their own cost
        // For parent items, sell_price_each will be calculated later using the correct formula:
        // (Parent's own sell price × quantity + children's line_total sum) ÷ parent quantity
        if (item.is_child_part) {
            // IMPORTANT: Don't override sell_price_each if it already has a correct hierarchical value
            // This preserves values calculated by the Python backend or hierarchical aggregation
            const currentSellPrice = safeNumber(item.sell_price_each);

            // Only recalculate if the current value is 0 or undefined
            if (currentSellPrice === 0) {
                // For child items, only include their own cost
                // Round up sell_price_each to whole number (integer)
                const markupMultiplier = 1 + (safeNumber(item.mark_up_item) / 100);
                item.sell_price_each = Math.ceil(safeNumber(item._base_cost) * markupMultiplier);
            }
            // For sell_cost_each, ONLY use VLC value - do NOT use _base_cost
            // sell_cost_each should come from VLC, not from calculated base cost
            item.sell_cost_each = (item.sell_cost_each && safeNumber(item.sell_cost_each) > 0)
                ? safeNumber(item.sell_cost_each)
                : 0;
        }
        // Note: Parent items' sell_price_each will be calculated in STEP 6.8 using the correct formula

        // Batch cost is always based on the total cost - ensure it's a number
        item.batch_cost = safeNumber(item.quantity) * safeNumber(totalCost);

        // Calculate line_total based on the item's own sell_price_each and quantity
        // This ensures we don't double-count children's costs in the line_total
        // Round up line_total to whole number
        item.line_total = Math.ceil(safeNumber(item.quantity) * safeNumber(item.sell_price_each));

        // Always return a number
        return safeNumber(totalCost);
    }

    // STEP 5: Process all items in bottom-up order (children first, then parents)
    // First, process all leaf nodes (items without children)
    frm.doc.items.forEach(item => {
        const hasChildren = frm.doc.items.some(child =>
            child.is_child_part &&
            ((child.parent_id && child.parent_id === item.name) ||
                (!child.parent_id && child.parent_part === item.part_number))
        );

        if (!hasChildren) {
            calculateTotalCost(item.name);
        }
    });

    // Then process items with children, in multiple passes if needed
    let unprocessedItems = true;
    let passCount = 0;
    const maxPasses = 10; // Safety limit

    while (unprocessedItems && passCount < maxPasses) {
        passCount++;
        unprocessedItems = false;

        frm.doc.items.forEach(item => {
            if (!item._processed) {
                // Check if all children are processed
                const children = frm.doc.items.filter(child =>
                    child.is_child_part &&
                    ((child.parent_id && child.parent_id === item.name) ||
                        (!child.parent_id && child.parent_part === item.part_number))
                );

                const allChildrenProcessed = children.every(child => child._processed);

                if (allChildrenProcessed) {
                    calculateTotalCost(item.name);
                } else {
                    unprocessedItems = true;
                }
            }
        });

    }

    // Final pass for any remaining items
    frm.doc.items.forEach(item => {
        if (!item._processed) {
            calculateTotalCost(item.name);
        }
    });

    // STEP 6.5: First calculate each item's own line_total (without children)
    frm.doc.items.forEach(item => {
        // Calculate each item's own line_total based on its own cost
        // Use the rounded sell_price_each value for consistency
        const sell_price = Math.round(safeNumber(item._base_cost) * (1 + (safeNumber(item.mark_up_item) / 100)));
        // Round up line_total to whole number
        item.line_total = Math.ceil(safeNumber(item.quantity) * sell_price);

        // Store the original line_total for hierarchical calculation
        item._own_line_total = item.line_total;

    });

    // STEP 6.6: Define a recursive function to calculate hierarchical line_total
    function calculateHierarchicalLineTotal(itemId) {
        const item = frm.doc.items.find(i => i.name === itemId);
        if (!item) return 0;

        // Start with the item's own line_total - ensure it's a number
        let total = safeNumber(item._own_line_total);

        // Find all direct children of this item
        const children = frm.doc.items.filter(i =>
            i.is_child_part &&
            ((i.parent_id && i.parent_id === item.name) ||
                (!i.parent_id && i.parent_part === item.part_number))
        );

        // Add each child's hierarchical line_total (recursively)
        for (const child of children) {
            // Ensure the recursive call result is treated as a number
            const childLineTotal = safeNumber(calculateHierarchicalLineTotal(child.name));
            total = safeNumber(total) + childLineTotal;
        }

        // Update the item's line_total with the hierarchical total - ensure it's a number
        item.line_total = safeNumber(total);

        // Always return a number
        return safeNumber(total);
    }

    // STEP 6.7: Apply the recursive calculation to all top-level items
    frm.doc.items.forEach(item => {
        if (!item.is_child_part) {
            calculateHierarchicalLineTotal(item.name);
        }
    });

    // STEP 6.8: Calculate sell_price_each and sell_cost_each according to the new requirements
    function calculateSellPriceEach() {
        // First, calculate base sell_price_each and initialize own_sell_cost_each for all items
        frm.doc.items.forEach(item => {
            // Store the base sell price with markup (without children) for later use
            // Round up sell_price_each to whole number (integer)
            item._own_sell_price_each = Math.ceil(safeNumber(item._base_cost) * (1 + (safeNumber(item.mark_up_item) / 100)));

            // Initialize own_sell_cost_each field with the original VLC value (before any hierarchical aggregation)
            // This ensures we always have the parent's own cost without children's costs
            // ONLY use VLC's sell_cost_each - do NOT use _base_cost for sell_cost_each calculations
            if (!item.own_sell_cost_each) {
                item.own_sell_cost_each = (item.sell_cost_each && safeNumber(item.sell_cost_each) > 0)
                    ? safeNumber(item.sell_cost_each)
                    : 0;  // If no VLC sell_cost_each, use 0 instead of _base_cost
            }

            // Set temporary variable _own_sell_cost_each from the persistent field for calculations
            item._own_sell_cost_each = safeNumber(item.own_sell_cost_each);
        });

        // Process items in reverse order (bottom-up) to ensure children are processed before parents
        const itemsInReverseOrder = [...frm.doc.items].sort((a, b) => b.idx - a.idx);

        // Helper function to get all direct children of an item
        function getDirectChildren(item) {
            return frm.doc.items.filter(i =>
                i.is_child_part &&
                ((i.parent_id && i.parent_id === item.name) ||
                    (!i.parent_id && i.parent_part === item.part_number))
            );
        }

        // Process all items in reverse order (bottom-up)
        for (const item of itemsInReverseOrder) {
            if (item.is_child_part) {
                // For child items that are also parents (middle-level items)
                const children = getDirectChildren(item);
                if (children.length > 0) {
                    // Middle-level item's sell_price_each should be its own base selling price plus all its children's line_total
                    // (children's line_totals accumulate to parent's sell_price_each)
                    item.sell_price_each = Math.ceil(safeNumber(item._own_sell_price_each));

                    // Add each child's line_total (which already accounts for quantity and hierarchical structure)
                    for (const child of children) {
                        // For sell_price_each, add child's line_total (with markup)
                        item.sell_price_each += safeNumber(child.line_total);
                    }

                    // For sell_cost_each: Simple hierarchical aggregation - parent's own cost + sum of children's sell_cost_each
                    let children_cost_sum = 0;
                    for (const child of children) {
                        children_cost_sum += safeNumber(child.sell_cost_each);
                    }
                    item.sell_cost_each = safeNumber(item._own_sell_cost_each) + children_cost_sum;
                } else {
                    // For leaf nodes (items without children), just use their own sell price/cost
                    item.sell_price_each = Math.ceil(safeNumber(item._own_sell_price_each));
                    item.sell_cost_each = safeNumber(item._own_sell_cost_each);
                }
            } else {
                // For root nodes (top-level parent items)
                // Root's sell_price_each should be the sum of all direct children's contributions
                const children = getDirectChildren(item);

                if (children.length > 0) {
                    // For root parent items that have children, apply special calculation
                    // Step 1: Calculate parent's own sell price multiplied by quantity
                    const parentOwnCost = Math.ceil(safeNumber(item._base_cost)) * (1 + (safeNumber(item.mark_up_item) / 100));
                    const parentTotalContribution = parentOwnCost * safeNumber(item.quantity);

                    // Step 2: Add children's line_total contributions
                    let childrenTotal = 0;
                    for (const child of children) {
                        childrenTotal += safeNumber(child.line_total);
                    }

                    // Step 3: Divide by parent quantity to get final sell_price_each
                    item.sell_price_each = Math.ceil(
                        (parentTotalContribution + childrenTotal) / Math.max(safeNumber(item.quantity), 1)
                    );

                    // For sell_cost_each: Simple hierarchical aggregation - parent's own cost + sum of children's sell_cost_each
                    let children_cost_sum = 0;
                    for (const child of children) {
                        children_cost_sum += safeNumber(child.sell_cost_each);
                    }
                    item.sell_cost_each = safeNumber(item._own_sell_cost_each) + children_cost_sum;
                } else {
                    // If the root node has no children, use its own sell price and cost
                    // This handles standalone root items
                    item.sell_price_each = Math.ceil(safeNumber(item._own_sell_price_each));
                    item.sell_cost_each = safeNumber(item._own_sell_cost_each);
                }
            }
        }

        // No special handling needed - the generic algorithm above handles all cases
    }

    // Apply the calculation
    calculateSellPriceEach();

    // STEP 6.9: Calculate line_total using hierarchical structure
    // First calculate each item's own line_total (without children)
    frm.doc.items.forEach(item => {
        // Calculate each item's own line_total based on its own cost
        // Use the rounded sell_price_each value for consistency
        const sell_price = Math.round(safeNumber(item._base_cost) * (1 + (safeNumber(item.mark_up_item) / 100)));
        // Round up line_total to whole number
        item.line_total = Math.ceil(safeNumber(item.quantity) * sell_price);

        // Store the original line_total for hierarchical calculation
        item._own_line_total = item.line_total;
    });

    // Define a recursive function to calculate hierarchical line_total
    function calculateHierarchicalLineTotal(itemId) {
        const item = frm.doc.items.find(i => i.name === itemId);
        if (!item) return 0;

        // Start with the item's own line_total - ensure it's a number
        let total = safeNumber(item._own_line_total);

        // Find all direct children of this item
        const children = frm.doc.items.filter(i =>
            i.is_child_part &&
            ((i.parent_id && i.parent_id === item.name) ||
                (!i.parent_id && i.parent_part === item.part_number))
        );

        // Add each child's hierarchical line_total (recursively)
        for (const child of children) {
            // Ensure the recursive call result is treated as a number
            const childLineTotal = safeNumber(calculateHierarchicalLineTotal(child.name));
            total = safeNumber(total) + childLineTotal;
        }

        // Update the item's line_total with the hierarchical total - ensure it's a number
        item.line_total = safeNumber(total);

        // Always return a number
        return safeNumber(total);
    }

    // Apply the recursive calculation to all top-level items
    frm.doc.items.forEach(item => {
        if (!item.is_child_part) {
            calculateHierarchicalLineTotal(item.name);
        }
    });

    // Use VLC's sell_cost_each for raw costs - do NOT use _base_cost for sell_cost_each calculations
    // VLC has already calculated the correct raw costs without markup
    frm.doc.items.forEach(item => {
        // ONLY use VLC's sell_cost_each - do NOT fallback to _base_cost
        let rawBaseCost;
        if (item.sell_cost_each && safeNumber(item.sell_cost_each) > 0) {
            rawBaseCost = safeNumber(item.sell_cost_each);
        } else {
            // If no VLC sell_cost_each available, use 0 instead of _base_cost
            rawBaseCost = 0;
        }

        // Store the raw base cost for later use - ensure it's a number
        item._raw_base_cost = safeNumber(rawBaseCost);

        // For child items, preserve the VLC sell_cost_each value - do NOT override with calculated costs
        // sell_cost_each should only come from VLC, not from any calculated values
    });

    // Calculate operation cost line for each item
    frm.doc.items.forEach(item => {
        // Store raw base cost as operation cost each
        item._op_cost_each = safeNumber(item._raw_base_cost);

        // Calculate operation cost line as quantity * operation cost each
        item._op_cost_line = safeNumber(item.quantity) * safeNumber(item._op_cost_each);

        // Reset the needs_recalculation flag
        item.needs_recalculation = false;
    });

    // Define a recursive function to calculate costs through the hierarchy
    function calculateChildCostAndLineTotal(itemId) {
        const item = frm.doc.items.find(i => i.name === itemId);
        if (!item) return { childCostEach: 0, lineTotal: 0 };

        // Find all direct children of this item
        const children = frm.doc.items.filter(i =>
            i.is_child_part &&
            ((i.parent_id && i.parent_id === item.name) ||
                (!i.parent_id && i.parent_part === item.part_number))
        );

        // Calculate child cost each as sum of children's contribution
        let childCostEach = 0;

        // Process all children to get their contribution to the parent
        for (const child of children) {
            // Recursively calculate child's values first
            calculateChildCostAndLineTotal(child.name);

            // For each child, calculate its contribution to the parent
            // This is the child's sell_cost_each * quantity
            const childContribution = safeNumber(child.sell_cost_each) * safeNumber(child.quantity);

            // Add child's contribution to the parent's child cost each - ensure it's a number
            childCostEach = safeNumber(childCostEach) + safeNumber(childContribution);
        }

        // Store child cost each for later use - ensure it's a number
        item._child_cost_each = safeNumber(childCostEach);

        // Calculate raw line total (without markup) - ensure it's a number
        const lineTotal = safeNumber(item._op_cost_line) + safeNumber(childCostEach);
        item._line_total_raw = safeNumber(lineTotal);

        // Note: sell_cost_each calculation is handled in calculateSellPriceEach() function
        // to avoid duplication and doubling issues. This function only handles line_total calculations.

        return {
            childCostEach: safeNumber(childCostEach),
            lineTotal: safeNumber(lineTotal)
        };
    }

    // Apply the calculation to all items
    frm.doc.items.forEach(item => {
        calculateChildCostAndLineTotal(item.name);
    });

    // STEP 8: Calculate delivery and grand total
    frm.doc.delivery_total = safeNumber(frm.doc.delivery_cost) * safeNumber(frm.doc.no_of_shipments);

    // Calculate grand total based on top-level parent items only
    // Since parent items now include their children's line_totals,
    // we must only include top-level items to avoid double-counting
    let grand_total = 0;
    frm.doc.items.forEach(item => {
        if (!item.is_child_part) {
            grand_total = safeNumber(grand_total) + safeNumber(item.line_total);
        }
    });

    // Only add delivery_total if add_to_parts is NOT enabled
    // If add_to_parts is enabled, delivery will be distributed to items instead
    if (!frm.doc.add_to_parts) {
        frm.doc.grand_total = safeNumber(grand_total) + safeNumber(frm.doc.delivery_total);
    } else {
        frm.doc.grand_total = safeNumber(grand_total);
    }


    // STEP 8: Handle delivery cost distribution if add_to_parts is set
    if (frm.doc.add_to_parts) {
        // Filter items to only include leaf nodes and independent parts for delivery cost allocation
        const eligibleItems = frm.doc.items.filter(item => isLeafNodeOrIndependent(item));

        // Calculate total quantity across eligible items only
        let total_qty = 0;
        eligibleItems.forEach(item => {
            total_qty = safeNumber(total_qty) + safeNumber(item.quantity);
        });

        // Calculate delivery cost per unit for eligible items
        const deliveryPerUnit = (frm.doc.delivery_total || 0) / (total_qty || 1);

        // Process all items, but only allocate delivery costs to eligible ones
        frm.doc.items.forEach(item => {
            // Only allocate delivery costs to leaf nodes and independent parts
            if (isLeafNodeOrIndependent(item)) {
                item.delivery_each = deliveryPerUnit;
            } else {
                // Parent parts with children should not receive delivery cost allocation
                item.delivery_each = 0;
            }

            // Populate delivery fields from document level to items (for reference)
            item.delivery_type = frm.doc.delivery_type || "";
            item.delivery_cost = frm.doc.delivery_cost || "";
            item.no_of_shipments = frm.doc.no_of_shipments || "";
            item.delivery_total = frm.doc.delivery_total || "";

            // Recalculate base cost with the new delivery_each value
            const base_cost = (
                safeNumber(item.material_price) +
                safeNumber(item.machining) +
                safeNumber(item.design) +
                safeNumber(item.finishing) +
                safeNumber(item.inspection) +
                safeNumber(item.other) +
                safeNumber(item.mass_finishing) +
                safeNumber(item.sub_con) +
                safeNumber(item.fixings) +
                safeNumber(item.tool_making) +
                safeNumber(item.turning) +
                safeNumber(item.edm) +
                safeNumber(item.assembly) +
                safeNumber(item.delivery_each)
            );

            // Update the item's base cost - ensure it's a number
            item._base_cost = Math.ceil(safeNumber(base_cost));

            // Reset processing flags for recalculation
            item._processed = false;
        });
    } else {
        // STEP 8.1: Handle delivery when add_to_parts is unchecked
        // Clear all delivery fields from items since delivery should be handled at document level
        frm.doc.items.forEach(item => {
            // When Add to Parts is unchecked, clear all delivery fields from items
            item.delivery_each = 0;
            item.delivery_type = "";
            item.delivery_cost = "";
            item.no_of_shipments = "";
            item.delivery_total = "";

            const base_cost = (
                safeNumber(item.material_price) +
                safeNumber(item.machining) +
                safeNumber(item.design) +
                safeNumber(item.finishing) +
                safeNumber(item.inspection) +
                safeNumber(item.other) +
                safeNumber(item.mass_finishing) +
                safeNumber(item.sub_con) +
                safeNumber(item.fixings) +
                safeNumber(item.tool_making) +
                safeNumber(item.turning) +
                safeNumber(item.edm) +
                safeNumber(item.assembly) +
                safeNumber(item.delivery_each)
            );

            // Update the item's base cost - ensure it's a number
            item._base_cost = Math.ceil(safeNumber(base_cost));

            // Reset processing flags for recalculation
            item._processed = false;
        });

        // Recalculate costs recursively through the hierarchy in bottom-up order
        // First, process all leaf nodes (items without children)
        frm.doc.items.forEach(item => {
            const hasChildren = frm.doc.items.some(child =>
                child.is_child_part &&
                ((child.parent_id && child.parent_id === item.name) ||
                    (!child.parent_id && child.parent_part === item.part_number))
            );

            if (!hasChildren) {
                calculateTotalCost(item.name);
            }
        });

        // Then process items with children, in multiple passes if needed
        let unprocessedItems = true;
        let passCount = 0;
        const maxPasses = 10; // Safety limit

        while (unprocessedItems && passCount < maxPasses) {
            passCount++;
            unprocessedItems = false;

            frm.doc.items.forEach(item => {
                if (!item._processed) {
                    // Check if all children are processed
                    const children = frm.doc.items.filter(child =>
                        child.is_child_part &&
                        ((child.parent_id && child.parent_id === item.name) ||
                            (!child.parent_id && child.parent_part === item.part_number))
                    );

                    const allChildrenProcessed = children.every(child => child._processed);

                    if (allChildrenProcessed) {
                        calculateTotalCost(item.name);
                    } else {
                        unprocessedItems = true;
                    }
                }
            });

        }

        // Final pass for any remaining items
        frm.doc.items.forEach(item => {
            if (!item._processed) {
                calculateTotalCost(item.name);
            }
        });

        // First calculate each item's own line_total (without children)
        frm.doc.items.forEach(item => {
            // Calculate each item's own line_total based on its own cost
            // Round up line_total to whole number
            const markupMultiplier = 1 + (safeNumber(item.mark_up_item) / 100);
            item.line_total = safeNumber(item.quantity) * (Math.ceil(safeNumber(item._base_cost)) * markupMultiplier);

            // Store the original line_total for hierarchical calculation
            item._own_line_total = safeNumber(item.line_total);
        });

        // Define a recursive function to calculate hierarchical line_total
        function calculateHierarchicalLineTotal(itemId) {
            const item = frm.doc.items.find(i => i.name === itemId);
            if (!item) return 0;

            // Start with the item's own line_total - ensure it's a number
            let total = safeNumber(item._own_line_total || 0);

            // Find all direct children of this item
            const children = frm.doc.items.filter(i =>
                i.is_child_part &&
                ((i.parent_id && i.parent_id === item.name) ||
                    (!i.parent_id && i.parent_part === item.part_number))
            );

            // Add each child's hierarchical line_total (recursively)
            for (const child of children) {
                // Ensure the recursive call result is treated as a number
                const childLineTotal = safeNumber(calculateHierarchicalLineTotal(child.name));
                total = safeNumber(total) + childLineTotal;
            }

            // Update the item's line_total with the hierarchical total - ensure it's a number
            item.line_total = safeNumber(total);

            // Always return a number
            return safeNumber(total);
        }

        // Apply the recursive calculation to all top-level items
        frm.doc.items.forEach(item => {
            if (!item.is_child_part) {
                calculateHierarchicalLineTotal(item.name);
            }
        });

        // Recalculate grand total including delivery_total (when add_to_parts is false)
        let updated_grand_total = 0;
        frm.doc.items.forEach(item => {
            if (!item.is_child_part) {
                updated_grand_total = safeNumber(updated_grand_total) + safeNumber(item.line_total);
            }
        });

        frm.doc.grand_total = safeNumber(updated_grand_total) + safeNumber(frm.doc.delivery_total);

    }

    // Handle add_to_parts specific grand total calculation
    if (frm.doc.add_to_parts) {
        // Recalculate grand total after delivery cost distribution
        let updated_grand_total = 0;
        frm.doc.items.forEach(item => {
            if (!item.is_child_part) {
                updated_grand_total = safeNumber(updated_grand_total) + safeNumber(item.line_total);
            }
        });

        frm.doc.grand_total = safeNumber(updated_grand_total);

        // Set delivery_total to 0 since it's now included in items to avoid double-counting
        frm.doc.delivery_total = 0;

    }

    // Update the UI
    frm.refresh_fields();

    return true;
}

function show_tree_visualization(frm) {
    if (!frm.doc.items || frm.doc.items.length === 0) {
        frappe.msgprint(__("No items found to visualize."));
        return;
    }

    // Build the tree structure
    const tree_data = build_tree_structure(frm.doc.items);

    // Generate HTML for the tree
    const tree_html = generate_tree_html(tree_data);

    // Show the tree in a dialog
    let d = new frappe.ui.Dialog({
        title: __("Tree Structure Visualization"),
        fields: [
            {
                fieldname: "tree_container",
                fieldtype: "HTML",
                options: `
                    <style>
                        .tree-container {
                            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                            font-size: 13px;
                            line-height: 1.4;
                            max-height: 600px;
                            overflow-y: auto;
                            background-color: #ffffff;
                            border: 1px solid #d1d5db;
                            border-radius: 6px;
                        }
                        .tree-header {
                            background-color: #f9fafb;
                            border-bottom: 2px solid #e5e7eb;
                            font-weight: 600;
                            color: #374151;
                        }
                        .tree-header .tree-row-content {
                            padding: 12px 16px;
                        }
                        .tree-row {
                            border-bottom: 1px solid #f3f4f6;
                            cursor: pointer;
                            transition: background-color 0.15s ease;
                        }
                        .tree-row:hover {
                            background-color: #f8fafc;
                        }
                        .tree-row.parent {
                            background-color: #fefefe;
                        }
                        .tree-row.child {
                            background-color: #fafafa;
                        }
                        .tree-row.orphaned {
                            background-color: #fef2f2;
                            border-left: 3px solid #ef4444;
                        }
                        .tree-row-content {
                            display: flex;
                            align-items: center;
                            padding: 8px 16px;
                            min-height: 40px;
                        }
                        .tree-part-column {
                            flex: 1;
                            display: flex;
                            align-items: center;
                            min-width: 250px;
                            padding-right: 20px;
                        }
                        .tree-qty-column {
                            flex: 0 0 100px;
                            text-align: center;
                            color: #6b7280;
                        }
                        .tree-price-column {
                            flex: 0 0 140px;
                            text-align: right;
                            padding-right: 20px;
                            color: #059669;
                            font-weight: 500;
                        }
                        .tree-total-column {
                            flex: 0 0 140px;
                            text-align: right;
                            padding-right: 16px;
                            color: #1f2937;
                            font-weight: 600;
                        }
                        .tree-icon {
                            margin-right: 8px;
                            font-size: 14px;
                        }
                        .tree-part-number {
                            color: #1f2937;
                            font-weight: 500;
                        }
                        .highlight {
                            background-color: #dbeafe !important;
                            border-left: 3px solid #3b82f6 !important;
                            transition: all 0.3s ease;
                        }
                    </style>
                    <div class="tree-container">
                        ${tree_html}
                    </div>
                `
            }
        ],
        size: "extra-large",
        primary_action_label: __("Close"),
        primary_action: function () {
            d.hide();
        }
    });

    d.show();

    // Add click handlers for tree rows
    $(d.wrapper).on('click', '.tree-row', function () {
        const row_name = $(this).data('row-name');
        if (row_name) {
            // Find the row in the grid and scroll to it
            const grid = frm.fields_dict.items.grid;
            const grid_row = grid.grid_rows.find(r => r.doc && r.doc.name === row_name);
            if (grid_row) {
                // Scroll to the row
                grid_row.row.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Highlight the row briefly
                $(grid_row.row).addClass('highlight');
                setTimeout(() => {
                    $(grid_row.row).removeClass('highlight');
                }, 2000);

                // Close the dialog
                d.hide();
            }
        }
    });
}

function build_tree_structure(items) {
    // Create maps for quick lookup
    const item_map = {};
    const children_map = {};

    // First pass: create item map and identify children
    items.forEach(item => {
        item_map[item.name] = item;

        if (item.is_child_part) {
            // Determine parent ID
            const parent_id = item.parent_id ||
                (item.parent_part ? items.find(p => p.part_number === item.parent_part)?.name : null);

            if (parent_id) {
                if (!children_map[parent_id]) {
                    children_map[parent_id] = [];
                }
                children_map[parent_id].push(item.name);
            }
        }
    });

    // Find root nodes (items that are not children of any other item)
    const root_nodes = items.filter(item => !item.is_child_part);

    // Also find orphaned child parts (child parts without valid parents)
    const orphaned_children = items.filter(item => {
        if (!item.is_child_part) return false;

        const parent_id = item.parent_id ||
            (item.parent_part ? items.find(p => p.part_number === item.parent_part)?.name : null);

        return !parent_id;
    });

    // Build tree recursively
    function build_node(item_name) {
        const item = item_map[item_name];
        if (!item) return null;

        const node = {
            name: item.name,
            part_number: item.part_number || '',
            description: item.description || '',
            quantity: item.quantity || 0,
            each_cost: item.each_cost || 0,
            sell_price_each: item.sell_price_each || 0,
            line_total: item.line_total || 0,
            is_child_part: item.is_child_part,
            is_duplicate: item.part_number && item.part_number.includes('*copy*'),
            children: []
        };

        // Add children recursively
        const child_ids = children_map[item_name] || [];
        child_ids.forEach(child_id => {
            const child_node = build_node(child_id);
            if (child_node) {
                node.children.push(child_node);
            }
        });

        return node;
    }

    // Build the complete tree
    const tree = [];
    root_nodes.forEach(root => {
        const root_node = build_node(root.name);
        if (root_node) {
            tree.push(root_node);
        }
    });

    // Add orphaned children as separate root nodes
    orphaned_children.forEach(orphan => {
        const orphan_node = build_node(orphan.name);
        if (orphan_node) {
            // Mark as orphaned for special styling
            orphan_node.is_orphaned = true;
            tree.push(orphan_node);
        }
    });

    return tree;
}

function generate_tree_html(tree_data) {
    if (!tree_data || tree_data.length === 0) {
        return '<div class="text-muted">No tree structure found.</div>';
    }

    function format_currency(value) {
        return frappe.format(value || 0, { fieldtype: 'Currency' });
    }

    function render_node(node, level = 0) {
        const indent = level * 20; // 20px per level

        // Determine node class and icon
        let node_class = 'tree-row';
        let icon = '';

        if (node.is_orphaned) {
            node_class += ' orphaned';
            icon = '⚠️';
        } else if (node.is_child_part) {
            node_class += ' child';
            icon = '📄';
        } else {
            node_class += ' parent';
            icon = node.children && node.children.length > 0 ? '📁' : '📄';
        }

        // Create the row HTML with table-like structure
        let html = `
            <div class="${node_class}" data-row-name="${node.name}" title="Click to navigate to this row" style="margin-left: ${indent}px;">
                <div class="tree-row-content">
                    <div class="tree-part-column">
                        <span class="tree-icon">${icon}</span>
                        <span class="tree-part-number">${node.part_number || 'N/A'}</span>
                    </div>
                    <div class="tree-qty-column">${node.quantity || 0}</div>
                    <div class="tree-price-column">${format_currency(node.sell_price_each)}</div>
                    <div class="tree-total-column">${format_currency(node.line_total)}</div>
                </div>
            </div>
        `;

        // Render children
        if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
                html += render_node(child, level + 1);
            });
        }

        return html;
    }

    // Create header
    let html = `
        <div class="tree-header">
            <div class="tree-row-content">
                <div class="tree-part-column"><strong>Part Number</strong></div>
                <div class="tree-qty-column" style="text-align: center;"><strong>Qty</strong></div>
                <div class="tree-price-column" style="text-align: right; padding-right: 20px;"><strong>Sell Price Each</strong></div>
                <div class="tree-total-column" style="text-align: right; padding-right: 16px;"><strong>Line Total</strong></div>
            </div>
        </div>
    `;

    // Add all root nodes
    tree_data.forEach(root => {
        html += render_node(root, 0);
    });

    return html;
}

/**
 * Validate delivery date hierarchy for child items
 * Ensures child delivery dates are not later than parent delivery dates
 */
function validateDeliveryDateHierarchy(frm, cdt, cdn) {
    const row = frappe.get_doc(cdt, cdn);

    // Skip validation if not a child part or no delivery date
    if (!row.is_child_part || !row.delivery_date) {
        return;
    }

    // Find the parent item
    let parent_item = null;

    // Try to find parent by parent_id first (more reliable)
    if (row.parent_id) {
        parent_item = frm.doc.items.find(item => item.name === row.parent_id);
    }

    // Fallback to finding parent by parent_part
    if (!parent_item && row.parent_part) {
        parent_item = frm.doc.items.find(item =>
            item.part_number === row.parent_part && !item.is_child_part
        );
    }

    // Skip validation if parent not found or parent has no delivery date
    if (!parent_item || !parent_item.delivery_date) {
        return;
    }

    // Convert dates for comparison
    const child_delivery_date = frappe.datetime.str_to_obj(row.delivery_date);
    const parent_delivery_date = frappe.datetime.str_to_obj(parent_item.delivery_date);

    // Validate that child delivery date is not later than parent
    if (child_delivery_date > parent_delivery_date) {
        frappe.msgprint({
            title: __('Invalid Delivery Date'),
            indicator: 'red',
            message: __('Child item "{0}" delivery date ({1}) cannot be later than parent item "{2}" delivery date ({3})', [
                row.part_number || 'Unknown',
                frappe.datetime.str_to_user(row.delivery_date),
                parent_item.part_number || 'Unknown',
                frappe.datetime.str_to_user(parent_item.delivery_date)
            ])
        });

        // Clear the invalid delivery date
        frappe.model.set_value(cdt, cdn, 'delivery_date', '');
        return false;
    }

    return true;
}

/**
 * Validate all delivery dates in the hierarchy
 * Called during form validation to ensure all dates are valid
 */
function validateAllDeliveryDates(frm) {
    let validation_errors = [];

    // Create a mapping of part_number to item for quick lookup
    const part_number_map = {};
    frm.doc.items.forEach(item => {
        if (item.part_number) {
            part_number_map[item.part_number] = item;
        }
    });

    // Validate each child item's delivery date against its parent
    frm.doc.items.forEach(item => {
        if (!item.is_child_part || !item.delivery_date) {
            return;
        }

        // Find the parent item
        let parent_item = null;

        // Try to find parent by parent_id first (more reliable)
        if (item.parent_id) {
            parent_item = frm.doc.items.find(i => i.name === item.parent_id);
        }

        // Fallback to finding parent by parent_part
        if (!parent_item && item.parent_part) {
            parent_item = part_number_map[item.parent_part];
        }

        // Skip validation if parent not found or parent has no delivery date
        if (!parent_item || !parent_item.delivery_date) {
            return;
        }

        // Convert dates for comparison
        const child_delivery_date = frappe.datetime.str_to_obj(item.delivery_date);
        const parent_delivery_date = frappe.datetime.str_to_obj(parent_item.delivery_date);

        // Check if child delivery date is later than parent
        if (child_delivery_date > parent_delivery_date) {
            validation_errors.push({
                child: item.part_number || 'Unknown',
                child_date: frappe.datetime.str_to_user(item.delivery_date),
                parent: parent_item.part_number || 'Unknown',
                parent_date: frappe.datetime.str_to_user(parent_item.delivery_date)
            });
        }
    });

    // Show validation errors if any
    if (validation_errors.length > 0) {
        let error_message = __('The following child items have delivery dates later than their parents:') + '<br><br>';
        validation_errors.forEach(error => {
            error_message += __('• Child "{0}" ({1}) cannot be later than parent "{2}" ({3})', [
                error.child, error.child_date, error.parent, error.parent_date
            ]) + '<br>';
        });

        frappe.msgprint({
            title: __('Invalid Delivery Dates'),
            indicator: 'red',
            message: error_message
        });

        return false;
    }

    return true;
}

// Simple function to check if linking child to parent would create circular reference
function would_create_circular_reference_simple(frm, childRow, parentRow) {
    // Check if the parent row has the child row in its ancestry
    let visited = new Set();
    let current = parentRow;

    while (current && current.is_child_part && !visited.has(current.name)) {
        visited.add(current.name);

        // Find parent of current item
        let parent_id = current.parent_id;
        if (!parent_id && current.parent_part) {
            let parent = (frm.doc.items || []).find(p => p.part_number === current.parent_part);
            parent_id = parent ? parent.name : null;
        }

        if (!parent_id) break;

        // If we find the child in the parent's ancestry, it would create a circular reference
        if (parent_id === childRow.name) {
            return true;
        }

        current = (frm.doc.items || []).find(i => i.name === parent_id);
    }

    return false;
}

// Check if potential_parent is a descendant of current_item (prevents bidirectional linking)
function is_descendant_of_simple(frm, current_item_id, potential_parent_id) {
    let visited = new Set();

    function check_descendants(item_id) {
        if (visited.has(item_id)) return false;
        visited.add(item_id);

        // Find all children of this item
        let children = (frm.doc.items || []).filter(item =>
            item.is_child_part && (
                item.parent_id === item_id ||
                (item.parent_part && frm.doc.items.find(p => p.name === item_id && p.part_number === item.parent_part))
            )
        );

        for (let child of children) {
            if (child.name === potential_parent_id) {
                return true; // Found the potential parent as a descendant
            }
            if (check_descendants(child.name)) {
                return true;
            }
        }
        return false;
    }

    return check_descendants(current_item_id);
}