{"actions": [], "allow_rename": 1, "creation": "2025-02-01 14:27:44.718446", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item", "qty", "uom", "column_break_gfr8", "costunit", "total_cost", "markup_percent", "price"], "fields": [{"fieldname": "item", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "link_filters": "[[\"Item\",\"custom_material_form\",\"=\",\"Fixing\"]]", "options": "<PERSON><PERSON>"}, {"columns": 1, "fieldname": "qty", "fieldtype": "Int", "in_list_view": 1, "label": "Qty"}, {"columns": 1, "fetch_from": "item.stock_uom", "fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM", "read_only": 1}, {"fieldname": "column_break_gfr8", "fieldtype": "Column Break"}, {"columns": 1, "fieldname": "costunit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Cost/unit"}, {"columns": 1, "fieldname": "total_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total Cost", "read_only": 1}, {"columns": 1, "fieldname": "markup_percent", "fieldtype": "Percent", "in_list_view": 1, "label": "Markup %"}, {"columns": 1, "fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-04-18 03:14:50.521869", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Line Fixing Detail", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}