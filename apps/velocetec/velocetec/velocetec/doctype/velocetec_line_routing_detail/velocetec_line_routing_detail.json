{"actions": [], "allow_rename": 1, "creation": "2025-01-28 06:10:28.810979", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["workstation_type", "operation", "hrs", "rate_hr", "column_break_axyr", "is_fixed", "cost_each", "markup_percent", "price"], "fields": [{"fieldname": "workstation_type", "fieldtype": "Link", "in_list_view": 1, "label": "Workstation Type", "options": "Workstation Type"}, {"fieldname": "operation", "fieldtype": "Link", "in_list_view": 1, "label": "Operation", "options": "Operation", "read_only_depends_on": "doc.workstation_type", "reqd": 1}, {"columns": 1, "fieldname": "hrs", "fieldtype": "Float", "in_list_view": 1, "label": "Hrs"}, {"columns": 1, "fetch_if_empty": 1, "fieldname": "rate_hr", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate/hr"}, {"fieldname": "column_break_axyr", "fieldtype": "Column Break"}, {"columns": 1, "default": "0", "fieldname": "is_fixed", "fieldtype": "Check", "label": "Is Fixed"}, {"columns": 1, "fieldname": "cost_each", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Line Cost"}, {"columns": 1, "default": "10", "fieldname": "markup_percent", "fieldtype": "Float", "in_list_view": 1, "label": "Markup %"}, {"columns": 1, "fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "search_fields": "workstation_type,is_fixed", "indexes": [{"fields": ["workstation_type"], "index_name": "vlrd_workstation_type_idx"}, {"fields": ["is_fixed"], "index_name": "vlrd_is_fixed_idx"}, {"fields": ["workstation_type", "is_fixed"], "index_name": "vlrd_workstation_type_is_fixed_idx"}], "modified": "2025-04-21 09:52:21.197568", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Line Routing Detail", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}