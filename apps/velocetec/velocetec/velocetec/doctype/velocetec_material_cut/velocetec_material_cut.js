// Copyright (c) 2025, Sydney Kibanga and contributors
// For license information, please see license.txt

// Global variables for dimension validation
let source_material_dimensions = {};
let material_form = null;
let validation_enabled = true;

frappe.ui.form.on('Velocetec Material Cut', {
    scan_barcode: function (frm) {
        if (frm.doc.scan_barcode) {
            process_scan(frm, frm.doc.scan_barcode);
        }
    },
    serial_no: function (frm) {
        if (frm.doc.serial_no) {
            get_material_details(frm, frm.doc.serial_no);

            // Update all child row serial number placeholders
            if (frm.doc.items) {
                frm.doc.items.forEach(function (row) {
                    frappe.model.set_value(row.doctype, row.name, 'serial_no', `${frm.doc.serial_no}-${row.idx}`);
                });
            }
        }
    },

    item_code: function (frm) {
        // When parent item_code changes, update all child rows with base item code
        if (frm.doc.item_code && frm.doc.items) {
            // Get base item code (remove dimension suffix if present)
            let base_item_code = get_base_item_code(frm.doc.item_code);

            frm.doc.items.forEach(function (row) {
                frappe.model.set_value(row.doctype, row.name, 'item_code', base_item_code);
            });
        }
    },

    material_size: function (frm) {
        // Load source material dimensions when material size changes
        if (frm.doc.material_size) {
            load_source_material_dimensions(frm);
        }
    },

    basic_rate: function (frm) {
        // When basic rate changes, recalculate child rates
        calculate_child_rates(frm);
    },
    onload: function (frm) {
        frm.set_query('material_size', 'items', function (doc) {
            let source_material_form = null;

            frappe.call({
                method: "frappe.client.get_value",
                args: {
                    doctype: "Item",
                    fieldname: "custom_material_form",
                    filters: { name: doc.item_code }
                },
                async: false,
                callback: function (response) {
                    if (response.message) {
                        source_material_form = response.message.custom_material_form;
                    }
                }
            });
            return {
                filters: {
                    material_form: source_material_form,
                }
            };
        });
        frm.set_df_property('item_code', 'read_only', 1);
        frm.set_df_property('material_size', 'read_only', 1);
        frm.set_df_property('warehouse', 'read_only', 1);
        frm.set_df_property('basic_rate', 'read_only', 1);

        // Initialize dimension validation system
        if (frm.doc.material_size) {
            load_source_material_dimensions(frm);
        }
    },

    refresh: function (frm) {
        // Initialize validation system if material size is available
        if (frm.doc.material_size) {
            load_source_material_dimensions(frm);
        }

        // Add view buttons similar to Stock Entry
        if (frm.doc.docstatus > 0) {
            // Add Stock Ledger view button
            frm.add_custom_button(__("Stock Ledger"), function () {
                frappe.route_options = {
                    voucher_no: frm.doc.repack_stock_entry,
                    from_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                    to_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                    company: frm.doc.company || frappe.defaults.get_user_default("Company"),
                    show_cancelled_entries: frm.doc.docstatus === 2,
                    ignore_prepared_report: true
                };
                frappe.set_route("query-report", "Stock Ledger");
            }, __("View"));

            // Add Serial / Batch Nos view button if there are items with serial numbers
            if (frm.doc.items && frm.doc.items.some(item => item.serial_no)) {
                frm.add_custom_button(__("Serial / Batch Nos"), function () {
                    frappe.route_options = {
                        voucher_no: frm.doc.repack_stock_entry,
                        voucher_type: "Stock Entry",
                        from_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                        to_date: frm.doc.posting_date || frappe.datetime.nowdate(),
                        company: frm.doc.company || frappe.defaults.get_user_default("Company")
                    };
                    frappe.set_route("query-report", "Serial and Batch Summary");
                }, __("View"));
            }
        }
    }
});

function get_base_item_code(item_code) {
    // Remove dimension suffix like " - 10x20x21" or " - 15x100"
    // Pattern matches " - " followed by numbers and 'x' characters
    const dimensionPattern = /\s*-\s*\d+x\d+(?:x\d+)?$/;
    return item_code.replace(dimensionPattern, '');
}

function get_material_details(frm, serial_no) {
    frappe.call({
        method: "velocetec.api.utils.get_material_details",
        args: {
            serial_no: serial_no
        },
        callback: function (response) {
            const data = response.message;
            if (data.item_code) {
                frm.set_value('item_code', data.item_code || null);
                frm.set_value('material_size', data.velocetec_material_size || null);
                frm.set_value('warehouse', data.warehouse || null);
                frm.set_value('basic_rate', data.incoming_rate || null);
            } else {
                frappe.msgprint("No material details were found for the given Serial No.");
            }
        }
    });
}

function process_scan(frm, scan_input) {
    frappe.call({
        method: "erpnext.stock.utils.scan_barcode",
        args: {
            search_value: scan_input
        },
        callback: function (response) {
            const data = response.message;
            if (data.serial_no) {
                frm.set_value('serial_no', data.serial_no || null);
                frm.set_value('scan_barcode', '');
            } else {
                frm.set_value('scan_barcode', '');
            }
        }
    });
}

function generate_serial_no_for_row(frm, row) {
    const source_serial_no = frm.doc.serial_no;

    if (!source_serial_no) {
        frappe.msgprint(__('Please select a Source Serial No.'));
        return;
    }

    let base_serial_no = `${source_serial_no}-${row.idx}`;
    const existing_serials_in_child_table = frm.doc.items.map((item) => item.serial_no);


    if (existing_serials_in_child_table.includes(base_serial_no)) {
        let counter = row.idx + 1;
        (function find_next_available_serial() {
            base_serial_no = `${source_serial_no}-${counter}`;
            if (!existing_serials_in_child_table.includes(base_serial_no)) {

                frappe.db.exists('Serial No', base_serial_no).then((exists) => {
                    if (!exists) {
                        frappe.model.set_value(row.doctype, row.name, 'serial_no', base_serial_no);
                    } else {
                        counter++;
                        find_next_available_serial();
                    }
                });
            } else {
                counter++;
                find_next_available_serial();
            }
        })();
    } else {

        frappe.db.exists('Serial No', base_serial_no).then((exists) => {
            if (!exists) {
                frappe.model.set_value(row.doctype, row.name, 'serial_no', base_serial_no);
            } else {
                let counter = row.idx + 1;
                (function find_next_available_serial() {
                    base_serial_no = `${source_serial_no}-${counter}`;
                    if (!existing_serials_in_child_table.includes(base_serial_no)) {
                        frappe.db.exists('Serial No', base_serial_no).then((exists) => {
                            if (!exists) {
                                frappe.model.set_value(row.doctype, row.name, 'serial_no', base_serial_no);
                            } else {
                                counter++;
                                find_next_available_serial();
                            }
                        });
                    } else {
                        counter++;
                        find_next_available_serial();
                    }
                })();
            }
        });
    }
}

function set_target_material_rate(frm, row) {
    frappe.call({
        method: "velocetec.api.utils.calculate_target_material_rate",
        args: {
            target_material_size: row.material_size,
            source_material_size: frm.doc.material_size,
            source_basic_rate: frm.doc.basic_rate,
            item_code: frm.doc.item_code
        },
        callback: function (response) {
            if (response.message && response.message.target_basic_rate) {
                frappe.model.set_value(row.doctype, row.name, 'basic_rate', response.message.target_basic_rate);
            } else {
                frappe.msgprint(__('Unable to calculate the basic rate for the target material size.'));
            }
        }
    });
}

frappe.ui.form.on('Velocetec Material Cut Item', {

    material_size: function (frm, cdt, cdn) {
        const row = locals[cdt][cdn];
        // Use base item code (remove dimension suffix if present)
        if (frm.doc.item_code) {
            let base_item_code = get_base_item_code(frm.doc.item_code);
            frappe.model.set_value(row.doctype, row.name, 'item_code', base_item_code);
        }

        // Show placeholder for serial number
        if (frm.doc.serial_no && !row.serial_no) {
            frappe.model.set_value(row.doctype, row.name, 'serial_no', `${frm.doc.serial_no}-${row.idx}`);
        }

        if (row.material_size && frm.doc.material_size && frm.doc.basic_rate) {
            set_target_material_rate(frm, row);
        }
    },

    basic_rate: function (frm, cdt, cdn) {
        const row = locals[cdt][cdn];

        const total_outgoing_value = frm.doc.basic_rate;
        frm.set_value('total_outgoing_value', total_outgoing_value);

        let total_incoming_value = 0;
        frm.doc.items.forEach(item => {
            total_incoming_value = total_incoming_value + item.basic_rate;
        });

        frm.set_value('total_incoming_value', total_incoming_value);

        const value_difference = total_outgoing_value - total_incoming_value;
        frm.set_value('value_difference', value_difference);
        frm.refresh_field('total_incoming_value');
        frm.refresh_field('value_difference');
    },

    // Add dimension validation triggers for child table changes
    x_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
        calculate_child_rates(frm);
    },
    y_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
        calculate_child_rates(frm);
    },
    z_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
        calculate_child_rates(frm);
    },
    d_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
        calculate_child_rates(frm);
    },
    l_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
        calculate_child_rates(frm);
    },

    // Trigger validation when rows are added or removed
    items_add: function (frm, cdt, cdn) {
        const row = locals[cdt][cdn];
        if (frm.doc.item_code) {
            // Use base item code (remove dimension suffix if present)
            let base_item_code = get_base_item_code(frm.doc.item_code);
            frappe.model.set_value(row.doctype, row.name, 'item_code', base_item_code);
        }

        // Show placeholder for serial number
        if (frm.doc.serial_no) {
            frappe.model.set_value(row.doctype, row.name, 'serial_no', `${frm.doc.serial_no}-${row.idx}`);
        }

        // Trigger validation after a short delay to allow field updates
        setTimeout(() => validate_dimensions_and_update_scrap(frm), 100);
    },

    items_remove: function (frm, cdt, cdn) {
        // Trigger validation when rows are removed
        setTimeout(() => validate_dimensions_and_update_scrap(frm), 100);
    }
});


// Custom CSS removed - using standard Frappe UI components


function load_source_material_dimensions(frm) {
    /**
     * Load source material dimensions and validation info from backend
     */
    if (!frm.doc.material_size) {
        return;
    }

    frappe.call({
        method: "get_dimension_validation_info",
        doc: frm.doc,
        callback: function (response) {
            if (response.message && !response.message.error) {
                const validation_info = response.message;

                // Store validation info globally
                source_material_dimensions = validation_info.source_dimensions;
                material_form = validation_info.material_form;

                // Add dimension usage indicator to the form
                add_dimension_usage_indicator(frm);

                // Update display with current validation info
                update_dimension_usage_display(frm, validation_info);
                show_validation_messages(frm, validation_info);

                if (validation_info.scrap_available) {
                    show_scrap_management_options(frm, validation_info.remaining_material, validation_info.material_form);
                } else {
                    remove_scrap_management_section();
                }
            } else if (response.message && response.message.error) {
                console.log("Validation info error:", response.message.error);
            }
        }
    });
}

function validate_dimensions_and_update_scrap(frm) {
    /**
     * Main validation function that calls backend for validation
     */
    if (!validation_enabled || !frm.doc.material_size) {
        return;
    }

    // Call backend for validation
    frappe.call({
        method: "get_dimension_validation_info",
        doc: frm.doc,
        callback: function (response) {
            if (response.message && !response.message.error) {
                const validation_info = response.message;

                // Update visual indicators
                update_dimension_usage_display(frm, validation_info);

                // Show validation messages
                show_validation_messages(frm, validation_info);

                // Calculate and display scrap
                if (validation_info.scrap_available) {
                    show_scrap_management_options(frm, validation_info.remaining_material, validation_info.material_form);
                } else {
                    remove_scrap_management_section();
                }
            }
        }
    });
}

function add_dimension_usage_indicator(frm) {
    /**
     * Initialize dimension monitoring (simplified)
     */
    // Just clear any existing dashboard content
    frm.dashboard.clear_headline();
}

function update_dimension_usage_display(frm, validation_info) {
    /**
     * Show dimension usage through simple alerts (no complex HTML)
     */
    // Only show alerts if there are issues
    if (validation_info.validation_errors && validation_info.validation_errors.length > 0) {
        // Errors will be shown by show_validation_messages function
        return;
    }

    // Show a simple success message if validation passes
    if (validation_info.total_used && Object.keys(validation_info.total_used).length > 0) {
        frappe.show_alert({
            message: 'Dimension validation passed',
            indicator: 'green'
        });
    }
}

function show_validation_messages(frm, validation_info) {
    /**
     * Show validation error and warning messages
     */
    // Clear previous messages
    frm.dashboard.clear_comment();

    if (validation_info.validation_errors && validation_info.validation_errors.length > 0) {
        const error_html = `
            <div style="color: #dc3545; background: #f8d7da; border: 1px solid #f5c6cb; padding: 8px; border-radius: 4px; margin: 5px 0;">
                <strong>⚠️ Dimension Validation Errors:</strong>
                <ul style="margin: 5px 0 0 20px;">
                    ${validation_info.validation_errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;
        frm.dashboard.add_comment(error_html, 'red', true);
    }
}

// Scrap calculation is now handled by the backend

function show_scrap_management_options(frm, remaining_material, material_form) {
    /**
     * Show scrap management options using standard Frappe UI
     */
    let scrap_dimensions_text = '';
    if (material_form === "Block") {
        scrap_dimensions_text = `${remaining_material.x} × ${remaining_material.y} × ${remaining_material.z}`;
    } else if (material_form === "Bar") {
        scrap_dimensions_text = `${remaining_material.d} × ${remaining_material.l}`;
    }

    // Show a simple message with action buttons
    frappe.show_alert({
        message: `Scrap material available: ${scrap_dimensions_text}`,
        indicator: 'blue'
    });

    // Add a button to the form toolbar for scrap creation
    if (!frm.custom_buttons['Create Scrap Entry']) {
        frm.add_custom_button('Create Scrap Entry', function () {
            create_scrap_entry(JSON.stringify(remaining_material), material_form);
        }, 'Actions');
    }
}

function remove_scrap_management_section() {
    /**
     * Remove scrap management button from toolbar
     */
    // Remove the custom button if it exists
    if (cur_frm && cur_frm.custom_buttons && cur_frm.custom_buttons['Create Scrap Entry']) {
        cur_frm.remove_custom_button('Create Scrap Entry', 'Actions');
    }
}

function create_scrap_entry(remaining_material_json, material_form) {
    /**
     * Create a scrap entry for remaining material
     */
    const remaining_material = JSON.parse(remaining_material_json);

    frappe.confirm(
        `Create a scrap entry for remaining material?<br><br>
        This will create a new item and stock entry for the leftover material.`,
        function () {
            // User confirmed, proceed with scrap creation
            frappe.call({
                method: "create_scrap_entry",
                doc: cur_frm.doc,
                args: {
                    remaining_dimensions: remaining_material,
                    material_form: material_form
                },
                callback: function (response) {
                    if (response.message && response.message.success) {
                        frappe.msgprint({
                            title: "Scrap Entry Created",
                            message: `
                                <p><strong>Scrap item created:</strong> ${response.message.scrap_item_code}</p>
                                <p><strong>Stock entry:</strong> ${response.message.stock_entry}</p>
                                <p>The scrap material has been added to your inventory.</p>
                            `,
                            indicator: 'green'
                        });

                        // Remove the scrap management section
                        remove_scrap_management_section();

                        // Refresh the form to show updated information
                        cur_frm.reload_doc();
                    } else {
                        frappe.msgprint({
                            title: "Error Creating Scrap Entry",
                            message: response.message ? response.message.error : "An unknown error occurred",
                            indicator: 'red'
                        });
                    }
                }
            });
        },
        function () {
            // User cancelled, do nothing
        }
    );
}


// Override the existing child table events to include validation
frappe.ui.form.on('Velocetec Material Cut Item', {
    x_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
    },
    y_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
    },
    z_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
    },
    d_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
    },
    l_dim: function (frm, cdt, cdn) {
        validate_dimensions_and_update_scrap(frm);
    }
});

// ============================================================================
// RATE CALCULATION FUNCTIONS
// ============================================================================

function calculate_child_rates(frm) {
    /**
     * Calculate child item rates based on dimensional splitting
     */
    if (!frm.doc.material_size || !frm.doc.items || !frm.doc.basic_rate) {
        return;
    }

    // Call backend to calculate rates
    frappe.call({
        method: "calculate_child_rates",
        doc: frm.doc,
        callback: function (response) {
            if (response.message) {
                // Refresh the child table to show updated rates
                frm.refresh_field('items');

                // Show success message
                frappe.show_alert({
                    message: 'Child rates calculated based on dimensions',
                    indicator: 'green'
                });
            }
        }
    });
}
