# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class VelocetecMaterialSize(Document):
    def validate(self):
        if self.material_form == "Block":
            self.name = f"{self.x_dim}x{self.y_dim}x{self.z_dim}"

        if self.material_form == "Bar":
            self.name = f"{self.d_dim}x{self.l_dim}"
