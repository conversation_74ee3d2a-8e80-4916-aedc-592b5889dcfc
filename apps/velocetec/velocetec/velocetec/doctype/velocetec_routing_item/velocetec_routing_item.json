{"actions": [], "creation": "2025-01-15 19:52:08.548166", "default_view": "List", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["sequence_id", "operation", "fixed_time", "col_break1", "workstation_type", "workstation", "time_in_mins", "costing_section", "hour_rate", "column_break_9", "operating_cost", "column_break_11", "batch_size", "set_cost_based_on_bom_qty", "cost_per_unit", "more_information_section", "description", "column_break_18", "image"], "fields": [{"depends_on": "eval:doc.parenttype == \"Routing\"", "fieldname": "sequence_id", "fieldtype": "Int", "label": "Sequence ID"}, {"fieldname": "operation", "fieldtype": "Link", "in_list_view": 1, "label": "Operation", "oldfieldname": "operation_no", "oldfieldtype": "Data", "options": "Operation", "reqd": 1}, {"default": "0", "description": "Operation time does not depend on quantity to produce", "fieldname": "fixed_time", "fieldtype": "Check", "in_list_view": 1, "label": "Fixed Time"}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "workstation_type", "fieldtype": "Link", "in_list_view": 1, "label": "Workstation Type", "options": "Workstation Type"}, {"depends_on": "eval:!doc.workstation_type", "fieldname": "workstation", "fieldtype": "Link", "label": "Workstation", "oldfieldname": "workstation", "oldfieldtype": "Link", "options": "Workstation"}, {"description": "In minutes", "fetch_from": "operation.total_operation_time", "fetch_if_empty": 1, "fieldname": "time_in_mins", "fieldtype": "Float", "in_list_view": 1, "label": "Operation Time", "oldfieldname": "time_in_mins", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "reqd": 1}, {"fieldname": "costing_section", "fieldtype": "Section Break", "label": "Costing"}, {"fieldname": "hour_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Hour Rate", "oldfieldname": "hour_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "precision": "2"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "operating_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Operating Cost", "oldfieldname": "operating_cost", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "read_only": 1}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fetch_from": "operation.batch_size", "fetch_if_empty": 1, "fieldname": "batch_size", "fieldtype": "Int", "label": "<PERSON><PERSON> Si<PERSON>"}, {"default": "0", "fieldname": "set_cost_based_on_bom_qty", "fieldtype": "Check", "label": "Set Operating Cost Based On Batch Quantity"}, {"depends_on": "eval:doc.batch_size > 0 && doc.set_cost_based_on_bom_qty", "fieldname": "cost_per_unit", "fieldtype": "Float", "label": "Cost Per Unit", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "more_information_section", "fieldtype": "Section Break", "label": "More Information"}, {"fieldname": "description", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Description", "oldfieldname": "opn_description", "oldfieldtype": "Text"}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "image", "fieldtype": "Attach", "label": "Image"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-04-18 03:14:48.903100", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Routing Item", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}