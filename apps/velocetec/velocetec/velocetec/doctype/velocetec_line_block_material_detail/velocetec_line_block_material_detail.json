{"actions": [], "allow_rename": 1, "creation": "2025-01-28 05:59:28.635605", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["material_type", "x", "y", "z", "column_break_zbbo", "cost_per_cubic_m", "amount", "markup_percent", "price"], "fields": [{"fieldname": "material_type", "fieldtype": "Link", "in_list_view": 1, "label": "Material Type", "link_filters": "[[\"Item\",\"custom_material_form\",\"=\",\"Block\"]]", "options": "<PERSON><PERSON>"}, {"columns": 1, "fieldname": "x", "fieldtype": "Float", "in_list_view": 1, "label": "X"}, {"columns": 1, "fieldname": "y", "fieldtype": "Float", "in_list_view": 1, "label": "Y"}, {"columns": 1, "fieldname": "z", "fieldtype": "Float", "in_list_view": 1, "label": "Z"}, {"fieldname": "column_break_zbbo", "fieldtype": "Column Break"}, {"fetch_from": "material_type.custom_price_per_cubic_meter", "fetch_if_empty": 1, "fieldname": "cost_per_cubic_m", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Cost <PERSON>", "read_only": 1}, {"columns": 1, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount"}, {"columns": 1, "fieldname": "markup_percent", "fieldtype": "Percent", "in_list_view": 1, "label": "Markup %"}, {"columns": 1, "fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-04-18 03:14:50.326885", "modified_by": "Administrator", "module": "Velocetec", "name": "Velocetec Line Block Material Detail", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}