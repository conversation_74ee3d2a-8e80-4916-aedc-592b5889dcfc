frappe.ui.form.on('Quotation', {
    refresh: function (frm) {
        // Only if the Quotation is submitted, add the Return button.

        frm.add_custom_button(__('Return to Costing'), function () {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Velocetec Costing",
                    filters: {
                        reference_doctype: "Quotation",
                        reference_name: frm.doc.name
                    },
                    fields: ["name"],
                    limit_page_length: 1
                },
                callback: function (r) {
                    if (r.message && r.message.length > 0) {
                        frappe.set_route("Form", "Velocetec Costing", r.message[0].name);
                    } else {
                        frappe.msgprint(__('No linked Costing found for this Quotation.'));
                    }
                }
            });
        }, __("Actions"));
    }
});
