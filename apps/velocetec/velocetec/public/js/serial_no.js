// Copyright (c) 2025, Sydney Kibanga and contributors
// For license information, please see license.txt

frappe.ui.form.on('Serial No', {
    refresh: function (frm) {
        frm.add_custom_button('Material Cut', function () {
            frappe.new_doc('Velocetec Material Cut', {
                serial_no: frm.doc.name 
            });
        }, 'Create');

        frm.add_custom_button('Material Transfer', function () {
            frappe.model.with_doctype('Stock Entry', function () {
                let doc = frappe.model.get_new_doc('Stock Entry');
                doc.stock_entry_type = 'Material Transfer';
                doc.from_warehouse = frm.doc.warehouse;

                frappe.call({
                    method: "velocetec.api.utils.get_material_details",
                    args: { serial_no: frm.doc.name },
                    callback: function (response) {
                        if (response.message) {
                            let child = frappe.model.add_child(doc, 'Stock Entry Detail', 'items');
                            child.material_size = response.message.velocetec_material_size;
                            child.to_material_size = response.message.velocetec_material_size;

                            frappe.set_route('Form', 'Stock Entry', doc.name).then(() => {
                                frappe.model.set_value('Stock Entry', doc.name, 'scan_barcode', frm.doc.name);
                                frappe.model.refresh_field('scan_barcode');
                            });
                        }
                    }
                });
            });
        }, 'Create');
    }
});
