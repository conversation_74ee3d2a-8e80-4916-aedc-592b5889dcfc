frappe.ui.form.on("Sales Order", {
    onload: function (frm) {
        if (frm.is_new()) {
            if (frm.doc.velocetec_costing && frm.doc.items && frm.doc.items.length > 0) {
                let hasQuotationRefs = frm.doc.items.some(item => item.quotation_item);
                if (hasQuotationRefs) {
                    updateDeliveryDatesFromServer(frm);
                }
            }
        }
    },

    on_submit: (frm) => {
        if (frm.doc.velocetec_costing) {
            createBOMCreator(frm.doc.name);
        }
    },

    refresh: function (frm) {
        frm.add_custom_button(__('Export Worksheet'), function() {
            frappe.call({
                method: 'velocetec.api.sales_order.export_sales_order_worksheet',
                args: { sales_order_name: frm.doc.name },
                callback: function (r) {
                    if (!r.exc) {
                        // Temporarily disabled Blob download
                        /*
                        const blob = new Blob([r.message], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        const link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download = `Sales Order ${frm.doc.name}.xlsx`;
                        link.click();
                        */
                    }
                }
            }).then(r => {
                if (r.message && r.message.file_url) {
                    window.open(r.message.file_url);
                }
                frm.reload_doc();
            });
        });
    }
});

function createBOMCreator(sales_order_name) {
    frappe.call({
        method: "velocetec.api.sales_order.create_bom_from_so",  // Adjust the path as needed to match backend
        args: { sales_order_name: sales_order_name },
        callback: function (r) {
            if (r.message && Array.isArray(r.message) && r.message.length > 0) {
                let createdBOMs = r.message;

                // Show an alert that BOM Creators were created
                frappe.show_alert({
                    message: __("BOM Creator(s) created successfully."),
                    indicator: "green"
                }, 5);

                // If only one BOM Creator was created, route directly to its form
                if (createdBOMs.length === 1) {
                    frappe.set_route("Form", "BOM Creator", createdBOMs[0]);
                }
                // If multiple BOM Creators were created, route to the list view with filter
                else {
                    frappe.route_options = { name: ["in", createdBOMs] };
                    frappe.set_route("List", "BOM Creator");
                }
            }
        }
    });
}


function updateDeliveryDatesFromServer(frm) {
    let quotation_item_names = frm.doc.items
        .filter(item => item.quotation_item)
        .map(item => item.quotation_item);

    if (quotation_item_names.length === 0) {
        return;
    }

    frappe.call({
        method: "velocetec.api.sales_order.update_delivery_dates_from_quotation_server",
        args: {
            quotation_items: quotation_item_names
        },
        callback: function (r) {
            if (r.message && r.message.status === "success" && r.message.delivery_dates) {
                let delivery_dates = r.message.delivery_dates;

                frm.doc.items.forEach(item => {
                    if (item.quotation_item && delivery_dates[item.quotation_item]) {
                        frappe.model.set_value(item.doctype, item.name, 'delivery_date', delivery_dates[item.quotation_item]);
                    }
                });

                frm.refresh_field('items');
            }
        }
    });
}
