Metadata-Version: 2.4
Name: frappe
Version: 15.72.5
Summary: Metadata driven, full-stack low code web framework
Author-email: Frappe Technologies Pvt Ltd <<EMAIL>>
Requires-Python: >=3.10,<3.14
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: Babel~=2.13.1
Requires-Dist: Click~=8.2.0
Requires-Dist: filelock~=3.13.1
Requires-Dist: filetype~=1.2.0
Requires-Dist: GitPython~=3.1.34
Requires-Dist: Jinja2~=3.1.2
Requires-Dist: Pillow~=11.0.0
Requires-Dist: PyJWT~=2.8.0
Requires-Dist: PyMySQL==1.1.1
Requires-Dist: pypdf~=3.17.0
Requires-Dist: PyPika==0.48.9
Requires-Dist: PyQRCode~=1.2.1
Requires-Dist: PyYAML~=6.0.2
Requires-Dist: RestrictedPython~=8.0
Requires-Dist: WeasyPrint==59.0
Requires-Dist: pydyf==0.10.0
Requires-Dist: Werkzeug~=3.0.1
Requires-Dist: Whoosh~=2.7.4
Requires-Dist: beautifulsoup4~=4.12.2
Requires-Dist: bleach-allowlist~=1.0.3
Requires-Dist: bleach[css]~=6.0.0
Requires-Dist: cairocffi==1.5.1
Requires-Dist: chardet~=5.1.0
Requires-Dist: croniter~=2.0.1
Requires-Dist: cryptography~=44.0.1
Requires-Dist: cssutils~=2.9.0
Requires-Dist: email-reply-parser~=0.5.12
Requires-Dist: gunicorn @ git+https://github.com/frappe/gunicorn@bb554053bb87218120d76ab6676af7015680e8b6
Requires-Dist: html5lib~=1.1
Requires-Dist: ipython~=8.15.0
Requires-Dist: ldap3~=2.9
Requires-Dist: markdown2~=2.4.8
Requires-Dist: MarkupSafe>=2.1.0,<3
Requires-Dist: maxminddb-geolite2==2018.703
Requires-Dist: num2words~=0.5.12
Requires-Dist: oauthlib~=3.2.2
Requires-Dist: openpyxl~=3.1.2
Requires-Dist: passlib~=1.7.4
Requires-Dist: pdfkit~=1.0.0
Requires-Dist: phonenumbers==8.13.55
Requires-Dist: premailer~=3.10.0
Requires-Dist: psutil~=5.9.5
Requires-Dist: psycopg2-binary~=2.9.1
Requires-Dist: pyOpenSSL~=25.0.0
Requires-Dist: pydantic~=2.10.2
Requires-Dist: pyotp~=2.8.0
Requires-Dist: python-dateutil~=2.8.2
Requires-Dist: pytz==2023.3
Requires-Dist: rauth~=0.7.3
Requires-Dist: redis~=4.5.5
Requires-Dist: hiredis~=2.2.3
Requires-Dist: requests-oauthlib~=1.3.1
Requires-Dist: requests~=2.32.0
Requires-Dist: rq==1.15.1
Requires-Dist: rsa>=4.1
Requires-Dist: semantic-version~=2.10.0
Requires-Dist: sentry-sdk~=1.45.1
Requires-Dist: sqlparse~=0.5.0
Requires-Dist: sql_metadata~=2.11.0
Requires-Dist: tenacity~=8.2.2
Requires-Dist: terminaltables~=3.1.10
Requires-Dist: traceback-with-variables~=2.0.4
Requires-Dist: typing_extensions>=4.6.1,<5
Requires-Dist: tomli~=2.0.1
Requires-Dist: xlrd~=2.0.1
Requires-Dist: zxcvbn~=4.4.28
Requires-Dist: markdownify~=0.14.1
Requires-Dist: boto3~=1.34.143
Requires-Dist: dropbox~=11.36.2
Requires-Dist: google-api-python-client~=2.172.0
Requires-Dist: google-auth-oauthlib~=1.2.2
Requires-Dist: google-auth~=2.40.3
Requires-Dist: posthog~=3.21.0
Requires-Dist: vobject~=0.9.7
Project-URL: Bug Reports, https://github.com/frappe/frappe/issues
Project-URL: Homepage, https://frappeframework.com/
Project-URL: Repository, https://github.com/frappe/frappe.git

<div align="center">
	<h1>
		<br>
		<a href="https://frappeframework.com">
			<img src=".github/frappe-framework-logo.svg" height="50">
		</a>
	</h1>
	<h3>
		a web framework with <a href="https://www.youtube.com/watch?v=LOjk3m0wTwg">"batteries included"</a>
	</h3>
	<h5>
		it's pronounced - <em>fra-pay</em>
	</h5>
</div>

<div align="center">
	<a target="_blank" href="#LICENSE" title="License: MIT"><img src="https://img.shields.io/badge/License-MIT-success.svg"></a>
	<a target="_blank" href="https://www.python.org/downloads/" title="Python version"><img src="https://img.shields.io/badge/python-%3E=_3.10-success.svg"></a>
	<a href="https://frappeframework.com/docs"><img src="https://img.shields.io/badge/docs-%F0%9F%93%96-success.svg"/></a>
	<a href="https://github.com/frappe/frappe/actions/workflows/server-tests.yml"><img src="https://github.com/frappe/frappe/actions/workflows/server-tests.yml/badge.svg"></a>
	<a href="https://github.com/frappe/frappe/actions/workflows/ui-tests.yml"><img src="https://github.com/frappe/frappe/actions/workflows/ui-tests.yml/badge.svg?branch=develop"></a>
	<a href="https://codecov.io/gh/frappe/frappe"><img src="https://codecov.io/gh/frappe/frappe/branch/develop/graph/badge.svg?token=XoTa679hIj"/></a>
</div>


Full-stack web application framework that uses Python and MariaDB on the server side and a tightly integrated client side library. Built for [ERPNext](https://erpnext.com).

<div align="center" style="max-height: 40px;">
	<a href="https://frappecloud.com/frappe/signup"><img src=".github/try-on-f-cloud-button.svg" height="40"></a>
	<a href="https://labs.play-with-docker.com/?stack=https://raw.githubusercontent.com/gavindsouza/install-scripts/main/frappe/pwd.yml"><img src="https://raw.githubusercontent.com/play-with-docker/stacks/master/assets/images/button.png" alt="Try in PWD" height="37"/></a>
</div>

> Login for the PWD site: (username: Administrator, password: admin)

## Table of Contents
* [Installation](#installation)
* [Contributing](#contributing)
* [Resources](#resources)
* [License](#license)

## Installation

### Production
* [Managed Hosting on Frappe Cloud](https://frappecloud.com/)
* [Easy install script using Docker images](https://github.com/frappe/bench/tree/develop#easy-install-script)
* [Manual install using Docker images](https://github.com/frappe/frappe_docker)

### Development
* [Easy install script using Docker images](https://github.com/frappe/bench/tree/develop#easy-install-script)
* [Development installation on bare metal](https://frappeframework.com/docs/user/en/installation)


## Contributing

1. [Code of Conduct](CODE_OF_CONDUCT.md)
1. [Contribution Guidelines](https://github.com/frappe/erpnext/wiki/Contribution-Guidelines)
1. [Security Policy](SECURITY.md)
1. [Translations](https://translate.erpnext.com)

## Resources

1. [frappeframework.com](https://frappeframework.com) - Official documentation of the Frappe Framework.
1. [frappe.school](https://frappe.school) - Pick from the various courses by the maintainers or from the community.

## License
This repository has been released under the [MIT License](LICENSE).

By contributing to Frappe, you agree that your contributions will be licensed under its MIT License.

