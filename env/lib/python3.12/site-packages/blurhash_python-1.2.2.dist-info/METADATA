Metadata-Version: 2.1
Name: blurhash-python
Version: 1.2.2
Summary: BlurHash encoder implementation for Python
Home-page: https://blurha.sh
Author: <PERSON><PERSON>
Author-email: atte.lauta<PERSON><EMAIL>
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: cffi
Requires-Dist: Pillow
Requires-Dist: six
Provides-Extra: testing
Requires-Dist: pytest ; extra == 'testing'

blurhash-python
===============

This is an encoder for the BlurHash algorithm. To find out more about BlurHash, see https://github.com/woltapp/blurhash.

Installation
------------
Install blurhash with pip
```
$ pip install blurhash-python
```
or pipenv
```
$ pipenv install blurhash-python
```

Usage
-----
Create blurhash from image file
```python
import blurhash

with open('image.jpg', 'rb') as image_file:
    hash = blurhash.encode(image_file, x_components=4, y_components=3)
```
Alternatively, scale the image to produce a faster hash, and create a blurhash from the in-memory image directly
```python
import blurhash
from PIL import Image

with Image.open('image.jpg') as image:
  image.thumbnail(( 100, 100 ))
  hash = blurhash.encode(image, x_components=4, y_components=3)
```
You can also pass file name as parameter to the function
```python
import blurhash

hash = blurhash.encode('image.jpg', x_components=4, y_components=3)
```
`y_components` and `x_components` parameters adjust the amount of
vertical and horizontal AC components in hashed image. Both parameters must
be `>= 1` and `<= 9`.

Development
-----------
Install development requirements and package in editable mode
```
$ pipenv install --dev
```

Tests
-----
Run test suite with `pytest` in virtual environment
```
$ pytest
```
Use `tox` to run test suite against all supported python versions
```
$ tox
```
