google/cloud/documentai/__init__.py,sha256=LOQtqwwZTzF9a9wB_rKXilcJhYSPj-pVgxmhEsgPJmM,5677
google/cloud/documentai/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/documentai/gapic_version.py,sha256=7muTM7FABy19xenFbrCS-mFZwex_Gjt6ko8zWvzG_OI,652
google/cloud/documentai/py.typed,sha256=w0TYjuD1R4wE6hXH3mZO1YeSd2YfRKJY8xSQgwJlob0,84
google/cloud/documentai_v1/__init__.py,sha256=bdPD_jpH8ZQCuMJKQ7nKgwX_lWPsulYk5C-uIJklgz8,5261
google/cloud/documentai_v1/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/documentai_v1/gapic_metadata.json,sha256=O1vui--xyENlJ0Gd96rXAUwse7G5ULmUa1W35MhB19M,9459
google/cloud/documentai_v1/gapic_version.py,sha256=7muTM7FABy19xenFbrCS-mFZwex_Gjt6ko8zWvzG_OI,652
google/cloud/documentai_v1/py.typed,sha256=w0TYjuD1R4wE6hXH3mZO1YeSd2YfRKJY8xSQgwJlob0,84
google/cloud/documentai_v1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/documentai_v1/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/__init__.py,sha256=OAdomue_ZECwFJBRkMMBAkBwTLTDznPpKcsCjYjF4zE,809
google/cloud/documentai_v1/services/document_processor_service/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/__pycache__/async_client.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/__pycache__/client.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/__pycache__/pagers.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/async_client.py,sha256=XgS5VVbnh8KRSlFSGItPYmlj-sGAQQcDwxYNgltNvck,145741
google/cloud/documentai_v1/services/document_processor_service/client.py,sha256=v8OyKQ2ln45MFgGdRF2cGuCH9LwjfqoyDyVsPoh2KRg,163136
google/cloud/documentai_v1/services/document_processor_service/pagers.py,sha256=vwDsa3ksXcCgyervnLpGOS80dyaFCK-HaJWwlz8aplw,28267
google/cloud/documentai_v1/services/document_processor_service/transports/__init__.py,sha256=-szq0PoOqAZsi8aBVvRNe9P0cjazmk2RlfctR12_6P0,1561
google/cloud/documentai_v1/services/document_processor_service/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/documentai_v1/services/document_processor_service/transports/base.py,sha256=XejNhZqboDjyd3Jy5sN5qAGEpcKi_FFCOMtuUUDsnbU,20600
google/cloud/documentai_v1/services/document_processor_service/transports/grpc.py,sha256=h94J6-sJgp_avLEpEz9Tr5SNopIvssoA_DfX6U6wHGc,48706
google/cloud/documentai_v1/services/document_processor_service/transports/grpc_asyncio.py,sha256=maRLM6gzLDZAhiFzvuGMZe6feLnK19xGeeU-4GAUdvg,56638
google/cloud/documentai_v1/services/document_processor_service/transports/rest.py,sha256=LEd6yF23EjKB0Qqn-CuON4LUcFgGprRCUxHh9-J0LSA,254166
google/cloud/documentai_v1/services/document_processor_service/transports/rest_base.py,sha256=z-T53rmU44GlZIL1aSLkSJsGO1zqk2FA-0w3A0fisYU,50737
google/cloud/documentai_v1/types/__init__.py,sha256=SMIAyXNMcCq_hehqAaAIKzM8tyvJ1SwiIhHCxRBVuVM,4873
google/cloud/documentai_v1/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/barcode.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/document.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/document_io.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/document_processor_service.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/document_schema.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/evaluation.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/geometry.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/operation_metadata.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/processor.cpython-312.pyc,,
google/cloud/documentai_v1/types/__pycache__/processor_type.cpython-312.pyc,,
google/cloud/documentai_v1/types/barcode.py,sha256=eUcoCAVgxjNRyHxTF4QvBtV-nwC4DFX4lrxrxzqr-n8,2729
google/cloud/documentai_v1/types/document.py,sha256=VhpwMQ2_Zi3FBJyCdYrCzOj7fqeeIM3F7wPsxAyGKJM,90758
google/cloud/documentai_v1/types/document_io.py,sha256=V_ar0R3pyCjvVyq57_PQKZQRRqm6SVBi2dXfqL1N2_k,11440
google/cloud/documentai_v1/types/document_processor_service.py,sha256=uWI22RNJWV4-Tzp58dV4u08cU89YaXhsOHtJrW7aKIo,57792
google/cloud/documentai_v1/types/document_schema.py,sha256=VkkppktPKYtDl7WHxxjq8PHBbUXkUZOzXEPJDkpvA9k,10037
google/cloud/documentai_v1/types/evaluation.py,sha256=a8mwuX2Mv5knhwaPqS386-Xa06VUq28igrZH-x0uDgQ,11932
google/cloud/documentai_v1/types/geometry.py,sha256=U7aZdAbnnxd3H66k1bdE1VXuDvD_NEg4Q9sxnAEwoQM,2608
google/cloud/documentai_v1/types/operation_metadata.py,sha256=3UV0sc1xUaW4h6fRxgTKTOkPcMZsY4OS1d3uws2uOGs,2822
google/cloud/documentai_v1/types/processor.py,sha256=5o4T6sSVuNL9gWZ103dSuO1sb-SmldKfSe71O4F2wk8,16712
google/cloud/documentai_v1/types/processor_type.py,sha256=uLh2II9eC8G3bmq3gAnaBfoDe2xvmq4051JX_28kkVA,3348
google/cloud/documentai_v1beta3/__init__.py,sha256=DbJSklSaBXFQXd64vDXeuRu6JqCtTE1jIJkN2GXV60Y,7133
google/cloud/documentai_v1beta3/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/__pycache__/gapic_version.cpython-312.pyc,,
google/cloud/documentai_v1beta3/gapic_metadata.json,sha256=NGpGnEBdL-zbPlGFg2lM3ZSb9dd1pZQ2d5hYoAAKw3c,12964
google/cloud/documentai_v1beta3/gapic_version.py,sha256=7muTM7FABy19xenFbrCS-mFZwex_Gjt6ko8zWvzG_OI,652
google/cloud/documentai_v1beta3/py.typed,sha256=w0TYjuD1R4wE6hXH3mZO1YeSd2YfRKJY8xSQgwJlob0,84
google/cloud/documentai_v1beta3/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/documentai_v1beta3/services/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/__init__.py,sha256=OAdomue_ZECwFJBRkMMBAkBwTLTDznPpKcsCjYjF4zE,809
google/cloud/documentai_v1beta3/services/document_processor_service/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/__pycache__/async_client.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/__pycache__/client.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/__pycache__/pagers.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/async_client.py,sha256=OZr_Dr5Y93Q0xcHWThOsyGwT24I_fbnT9yH4kmwFX7E,153332
google/cloud/documentai_v1beta3/services/document_processor_service/client.py,sha256=k3gapLRr84Mq3lgfhEusJSLuAp9m0Joa_ec-riPRACw,170599
google/cloud/documentai_v1beta3/services/document_processor_service/pagers.py,sha256=wem1_ftWgsrUxx4qRY-YACCdU6ozfCgnuUkvnZS2Kgg,28432
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__init__.py,sha256=-szq0PoOqAZsi8aBVvRNe9P0cjazmk2RlfctR12_6P0,1561
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_processor_service/transports/base.py,sha256=MtN4HGvHiTm9BSVoqxZPqut1oblG1lJff9wYV01ECRQ,21109
google/cloud/documentai_v1beta3/services/document_processor_service/transports/grpc.py,sha256=k1srBYOpzA-XAoJxgX3il3ou_VzhGHnKuTiXD1kWz1s,50158
google/cloud/documentai_v1beta3/services/document_processor_service/transports/grpc_asyncio.py,sha256=Mh9PghgUKz34LZuXd7LTDisC3D7CrvMBl-bY9GUMGUw,58315
google/cloud/documentai_v1beta3/services/document_processor_service/transports/rest.py,sha256=SFTjTx8KppXqfsmAbyH6C3YY9yDUuhviJuOTr33p5WM,265282
google/cloud/documentai_v1beta3/services/document_processor_service/transports/rest_base.py,sha256=FxHQynavpTaYzjVwj-G7JmNoKbtXq-_Cb0XP0JIWcrE,52830
google/cloud/documentai_v1beta3/services/document_service/__init__.py,sha256=ghacn4DpctG0P4jSAr0SvgWjyz3Rqw-9Q-v1s9agboc,773
google/cloud/documentai_v1beta3/services/document_service/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/__pycache__/async_client.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/__pycache__/client.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/__pycache__/pagers.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/async_client.py,sha256=548VJQmkqOItCu3HKTmZAR7UcLTW98aYbnWnuwtGZRc,62054
google/cloud/documentai_v1beta3/services/document_service/client.py,sha256=khjOabnFA0go5reoSh_QCqDpCpFSvsC72TtNj_uvZ7g,79637
google/cloud/documentai_v1beta3/services/document_service/pagers.py,sha256=rwJqVyMxTJCfxYoMdPlK8pQMyMsns3BVfFk_N6O7qp0,7903
google/cloud/documentai_v1beta3/services/document_service/transports/__init__.py,sha256=MKgTkVVi_d-7ZFZrbL2o-EL4MagxcL9wle7g-dcOKLg,1414
google/cloud/documentai_v1beta3/services/document_service/transports/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/transports/__pycache__/base.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/transports/__pycache__/grpc.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/transports/__pycache__/grpc_asyncio.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/transports/__pycache__/rest.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/transports/__pycache__/rest_base.cpython-312.pyc,,
google/cloud/documentai_v1beta3/services/document_service/transports/base.py,sha256=oaUSs6bHEe3qfYqwM30VSi9PjwtwCFxfTVG-krYEqWI,11566
google/cloud/documentai_v1beta3/services/document_service/transports/grpc.py,sha256=jSGsTY45hnxpSiw5bqmF2USTmqz7flE9fUTi4lveFoc,28254
google/cloud/documentai_v1beta3/services/document_service/transports/grpc_asyncio.py,sha256=HqbVUrnmWfKTHQSAnX0LegWdhra6c7_jafLZA7HSd7g,31632
google/cloud/documentai_v1beta3/services/document_service/transports/rest.py,sha256=uzfdWwhGGtwCYcWu-2vGCl5U1RnXIgB0MGDsX-dspA0,104470
google/cloud/documentai_v1beta3/services/document_service/transports/rest_base.py,sha256=R85fQwiEg3OEZ5C6b8dpA9EpUziF2TIM5JbZlua2vvE,22462
google/cloud/documentai_v1beta3/types/__init__.py,sha256=Df5WW3l8E6IsjE5tYCgoaLfuIjbxNkYMWkfIEfK3LS0,6576
google/cloud/documentai_v1beta3/types/__pycache__/__init__.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/barcode.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/dataset.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/document.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/document_io.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/document_processor_service.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/document_schema.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/document_service.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/evaluation.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/geometry.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/operation_metadata.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/processor.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/__pycache__/processor_type.cpython-312.pyc,,
google/cloud/documentai_v1beta3/types/barcode.py,sha256=7_ZDc8n51jEt_q_GVl_bJ32zaC-FLOaqWUOiVAL4Jjc,2734
google/cloud/documentai_v1beta3/types/dataset.py,sha256=zsmo7mG8r3jPbsi_TJ6vmj6fa-ne65fVqL0g0mK-xx8,11721
google/cloud/documentai_v1beta3/types/document.py,sha256=eNAMyzrqcTZ35dgClzDuh-Oi6OvzyiVzLWBWNlOHWQ0,104382
google/cloud/documentai_v1beta3/types/document_io.py,sha256=HSPPLO8glDTBcK_kQwCj3QyQ2pfLzs4kw5w2FJvziPI,11490
google/cloud/documentai_v1beta3/types/document_processor_service.py,sha256=6c3p2d8-i7e5z4NaPhAQBMA6tKCq_FTmBqPXX140BTY,70405
google/cloud/documentai_v1beta3/types/document_schema.py,sha256=CoXkKY99tcQmOVgZ3fg4sW9wd_M9dh7Z8FfPvCd5jJU,14156
google/cloud/documentai_v1beta3/types/document_service.py,sha256=4710UkDoESwW9g22p9RXhjpnnIf1Z-00Jjt30E6PDng,22809
google/cloud/documentai_v1beta3/types/evaluation.py,sha256=Ioxwoivv0mvMxF0xSXZMQgzmecCjCt0TqWgX67JYY4E,11982
google/cloud/documentai_v1beta3/types/geometry.py,sha256=OLqemjejuXZqOusuZkjxvjylTeUAdZmIcQzWYhlkTQI,2623
google/cloud/documentai_v1beta3/types/operation_metadata.py,sha256=kaBjEq5KJCIp8u5rSSoWEgq-eFrX3vwMddXHx3C-2Fo,2832
google/cloud/documentai_v1beta3/types/processor.py,sha256=W3z80pzXrRXV0ytqRlDHTggVolL0oLh01liyU-InWKQ,16787
google/cloud/documentai_v1beta3/types/processor_type.py,sha256=a48cXM2608iXQTmiH-vBMAvAXm_UQHssxkusZqO44tU,3358
google_cloud_documentai-3.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_documentai-3.5.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_documentai-3.5.0.dist-info/METADATA,sha256=gN29OKgPQj4GxcG18z4-jbHsT9NUKHVKNfqfXZxW0YI,9457
google_cloud_documentai-3.5.0.dist-info/RECORD,,
google_cloud_documentai-3.5.0.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
google_cloud_documentai-3.5.0.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
